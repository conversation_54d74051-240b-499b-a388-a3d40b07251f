import React, { useState, useContext } from 'react';
import { useNavigation } from '@react-navigation/native';
import { View, Text, TextInput, TouchableOpacity, KeyboardAvoidingView, Platform, ScrollView, Keyboard, TouchableWithoutFeedback, Image } from 'react-native';
import createStyles from './Style_Schema/uid_schema';
import { ThemeContext } from '../contexts/ThemeContext';
import { getBusMasterData } from '../scripts/DynamoData';
import LanguageSelectionDropdown from '../components/LanguageSelectionDropdown';
import { LinearGradient } from 'expo-linear-gradient';
import AsyncStorage from '@react-native-async-storage/async-storage';
import ColorModeToggle from '../components/ColorModeToggle';
import LogoImage from '../assets/images/bbplogo.png';
import { useTranslation } from 'react-i18next';

const UIDScreen = () => {
  const { theme } = useContext(ThemeContext);
  const { t } = useTranslation();
  const styles = createStyles(theme);
  const navigation = useNavigation();
  const [uid, setUid] = useState('');
  const [error, setError] = useState('');

  const handleSubmit = async () => {
    // Dismiss keyboard when submitting
    Keyboard.dismiss();

    if (uid.trim()) {
      try {
        const userData = await getBusMasterData("BusMaster-DB", uid);
        if (userData) {
          setError('');
          // Store UID and set isLoggedIn flag in AsyncStorage
          await AsyncStorage.setItem('uid', uid);
          await AsyncStorage.setItem('isLoggedIn', 'true');
          // Pass the master record to the Number screen
          navigation.navigate('Number', { masterData: userData });
        } else {
          setError("Invalid uid");
        }
      } catch (err) {
        console.error("UID verification error:", err);
        setError("An error occurred. Please try again.");
      }
    }
  };

  return (
    <View style={{ flex: 1, backgroundColor: theme.background }}>
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={styles.container}
        keyboardVerticalOffset={Platform.OS === "ios" ? 64 : 0}
      >
        <View style={{ flex: 1 }}>
          <ScrollView
            contentContainerStyle={styles.scrollContainer}
            keyboardShouldPersistTaps="always"
            keyboardDismissMode="none"
            showsVerticalScrollIndicator={false}
          >
            {/* Theme toggle in top-left corner */}
            <View style={styles.headerContainer}>
              <View style={styles.themeContainer}>
                <ColorModeToggle />
              </View>

              {/* Language Dropdown in the top-right corner */}
              <View style={styles.languageContainer}>
                <LanguageSelectionDropdown />
              </View>
            </View>

            {/* Logo Section */}
            <View style={styles.logoContainer}>
              <View style={styles.logoBox}>
                <Image
                  source={LogoImage}
                  style={styles.logoImage}
                  resizeMode="contain"
                />
              </View>
            </View>

            {/* Input Section */}
            <View style={styles.inputContainer}>
              <Text style={[styles.label, { color: theme.text }]}>{t('uid.title')}</Text>
              <TextInput
                style={[styles.input, {
                  color: theme.text,
                  backgroundColor: theme.inputBackground || theme.normal_background,
                  borderColor: theme.inputBorder || theme.primary,
                  textAlignVertical: 'top', // Helps with multiline text alignment
                  paddingTop: 12 // Add some padding for multiline text
                }]}
                placeholder={t('uid.placeholder')}
                placeholderTextColor={theme.placeholderColor || '#999'}
                value={uid}
                onChangeText={(text) => {
                  setUid(text.replace(/[^0-9]/g, ''));
                  setError('');
                }}
                keyboardType="number-pad"
                returnKeyType="done"
                onSubmitEditing={handleSubmit}
                maxLength={10}
                autoCorrect={false}
                blurOnSubmit={false}
                contextMenuHidden={true}
                importantForAutofill="no"
                autoCapitalize="none"
                spellCheck={false}
                multiline={true} // Allow multiple lines for placeholder
                numberOfLines={2} // Allow up to 2 lines
              />

              {error ? <Text style={[styles.errorText, { color: theme.error || 'red' }]}>{t(error === "Invalid uid" ? 'uid.errors.invalid' : 'uid.errors.general')}</Text> : null}
              <Text style={[styles.helpText, { color: theme.primary }]}>{t('uid.helpText')}</Text>
            </View>

            {/* Next Button */}
            <View style={styles.buttonContainer}>
              <TouchableOpacity
                onPress={handleSubmit}
                disabled={!uid.trim()}
                activeOpacity={0.8}
              >
                <LinearGradient
                  colors={theme.buttonGradient}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 0 }}
                  style={[styles.button, { opacity: uid.trim() ? 1 : 0.5 }]}
                >
                  <Text style={styles.buttonText}>{t('uid.nextButton')}</Text>
                </LinearGradient>
              </TouchableOpacity>
            </View>
          </ScrollView>
        </View>
      </KeyboardAvoidingView>
    </View>
  );
};

export default UIDScreen;
