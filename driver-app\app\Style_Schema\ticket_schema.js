import { StyleSheet, Dimensions } from 'react-native';

const { width, height } = Dimensions.get('window');

const createStyles = (theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.backgroundColor,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 50,
    paddingBottom: 20,
    backgroundColor: theme.backgroundColor,
  },
  backButton: {
    padding: 10,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  fareContainer: {
    alignItems: 'center',
    marginBottom: 30,
  },
  fareLabel: {
    fontSize: 18,
    color: theme.textColor,
    marginBottom: 5,
  },
  fareAmount: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FF0000',
  },
  dropdownContainer: {
    marginBottom: 20,
  },
  dropdownLabel: {
    fontSize: 16,
    color: theme.textColor,
    marginBottom: 8,
  },
  dropdown: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: theme.cardBackgroundColor,
    borderRadius: 8,
    paddingHorizontal: 15,
    paddingVertical: 12,
    borderWidth: 1,
    borderColor: theme.borderColor || '#E0E0E0',
  },
  dropdownText: {
    fontSize: 16,
    color: theme.textColor,
    flex: 1,
  },
  quickActionsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 30,
    marginTop: 10,
  },
  quickActionButton: {
    backgroundColor: theme.cardBackgroundColor,
    borderRadius: 20,
    paddingHorizontal: 20,
    paddingVertical: 10,
    marginBottom: 10,
    minWidth: '45%',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: theme.borderColor || '#E0E0E0',
  },
  quickActionText: {
    fontSize: 14,
    color: theme.textColor,
  },
  passengerTypeContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 40,
  },
  passengerTypeButton: {
    flex: 1,
    backgroundColor: theme.cardBackgroundColor,
    borderRadius: 8,
    paddingVertical: 12,
    marginHorizontal: 5,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: theme.borderColor || '#E0E0E0',
  },
  passengerTypeButtonSelected: {
    backgroundColor: theme.primaryColor || '#6366F1',
    borderColor: theme.primaryColor || '#6366F1',
  },
  passengerTypeText: {
    fontSize: 14,
    color: theme.textColor,
  },
  passengerTypeTextSelected: {
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  actionButtonsContainer: {
    marginBottom: 30,
  },
  printButton: {
    backgroundColor: '#6366F1',
    borderRadius: 25,
    paddingVertical: 15,
    alignItems: 'center',
    marginBottom: 20,
  },
  printButtonText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  bottomButtonsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  luggageButton: {
    flex: 1,
    backgroundColor: theme.cardBackgroundColor,
    borderRadius: 25,
    paddingVertical: 15,
    alignItems: 'center',
    marginRight: 10,
    borderWidth: 1,
    borderColor: theme.borderColor || '#E0E0E0',
  },
  luggageButtonText: {
    fontSize: 16,
    color: theme.textColor,
  },
  cancelButton: {
    flex: 1,
    backgroundColor: theme.cardBackgroundColor,
    borderRadius: 25,
    paddingVertical: 15,
    alignItems: 'center',
    marginLeft: 10,
    borderWidth: 1,
    borderColor: theme.borderColor || '#E0E0E0',
  },
  cancelButtonText: {
    fontSize: 16,
    color: theme.textColor,
  },
});

export default createStyles;
