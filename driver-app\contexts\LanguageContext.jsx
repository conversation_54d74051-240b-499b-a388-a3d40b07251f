import React, { createContext, useState, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import { I18nManager } from 'react-native';

// Import translations
import enTranslations from '../translations/en.json';
// import hiTranslations from '../translations/hi.json';
// import urTranslations from '../translations/ur.json';
import taTranslations from '../translations/ta.json'; 
// Initialize i18next
i18n
  .use(initReactI18next)
  .init({
    resources: {
      en: { translation: enTranslations },
    //   hi: { translation: hiTranslations },
    //   ur: { translation: urTranslations },
      ta: { translation: taTranslations } 
    },
    lng: 'en', // Default language
    fallbackLng: 'en',
    interpolation: {
      escapeValue: false
    }
  });

// Create the language context
export const LanguageContext = createContext();

export const LanguageProvider = ({ children }) => {
  const [currentLanguage, setCurrentLanguage] = useState('en');
  
  // Load saved language preference
  useEffect(() => {
    const loadLanguage = async () => {
      try {
        const savedLanguage = await AsyncStorage.getItem('userLanguage');
        if (savedLanguage) {
          changeLanguage(savedLanguage);
        }
      } catch (error) {
        console.error('Error loading language preference:', error);
      }
    };
    
    loadLanguage();
  }, []);
  
  // Function to change language
  const changeLanguage = async (language) => {
    try {
      // Change language in i18n
      await i18n.changeLanguage(language);
      
      // Handle RTL languages (like Urdu)
      const isRTL = language === 'ur';
      if (I18nManager.isRTL !== isRTL) {
        I18nManager.forceRTL(isRTL);
        // Note: In a real app, you might want to reload the app here
        // to properly apply RTL changes
      }
      
      // Save language preference
      await AsyncStorage.setItem('userLanguage', language);
      
      // Update state
      setCurrentLanguage(language);
    } catch (error) {
      console.error('Error changing language:', error);
    }
  };
  
  return (
    <LanguageContext.Provider value={{ currentLanguage, changeLanguage }}>
      {children}
    </LanguageContext.Provider>
  );
};