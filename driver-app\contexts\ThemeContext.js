import React, { createContext, useState, useEffect } from 'react';
import { mainColors, lightMode, darkMode } from '../constants/Colors';
import AsyncStorage from '@react-native-async-storage/async-storage';

const lightTheme = {
  ...mainColors,
  ...lightMode,
  buttonTextColor: '#FFFFFF',
  inputBackground: '#FFFFFF',
  inputBorder: mainColors.primary,
  placeholderColor: '#999999',
};

const darkTheme = {
  ...mainColors,
  ...darkMode,
  buttonTextColor: '#FFFFFF',
  inputBackground: '#333333',
  inputBorder: '#555555',
  placeholderColor: '#777777',
};

export const ThemeContext = createContext({
  theme: lightTheme,
  mode: 'light',
  toggleTheme: () => {},
});

export const ThemeProvider = ({ children }) => {
  const [mode, setMode] = useState('light');
  const [theme, setTheme] = useState(lightTheme);

  // Load saved theme preference on startup
  useEffect(() => {
    const loadThemePreference = async () => {
      try {
        const savedMode = await AsyncStorage.getItem('themeMode');
        if (savedMode) {
          if (savedMode === 'dark') {
            setTheme(darkTheme);
            setMode('dark');
          } else {
            setTheme(lightTheme);
            setMode('light');
          }
        }
      } catch (error) {
        console.error('Error loading theme preference:', error);
      }
    };
    
    loadThemePreference();
  }, []);

  const toggleTheme = async () => {
    try {
      if (mode === 'light') {
        setTheme(darkTheme);
        setMode('dark');
        await AsyncStorage.setItem('themeMode', 'dark');
      } else {
        setTheme(lightTheme);
        setMode('light');
        await AsyncStorage.setItem('themeMode', 'light');
      }
    } catch (error) {
      console.error('Error saving theme preference:', error);
    }
  };

  return (
    <ThemeContext.Provider value={{ theme, mode, toggleTheme }}>
      {children}
    </ThemeContext.Provider>
  );
};
