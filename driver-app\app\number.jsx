import React, { useState, useContext, useEffect } from 'react';
import { useNavigation, useRoute } from '@react-navigation/native';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  // KeyboardAvoidingView,
  Platform,
  Keyboard,
  // TouchableWithoutFeedback,
  Image
} from 'react-native';
import createStyles from './Style_Schema/number_schema';
import { ThemeContext } from '../contexts/ThemeContext';
import LanguageSelectionDropdown from '../components/LanguageSelectionDropdown';
import { LinearGradient } from 'expo-linear-gradient';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { getAvailableBuses } from '../scripts/DynamoData';
import ColorModeToggle from '../components/ColorModeToggle';
import LogoImage from '../assets/images/bbplogo.png';
import { useTranslation } from 'react-i18next';

const NumberScreen = () => {
  const { theme } = useContext(ThemeContext);
  const { t } = useTranslation();
  const styles = createStyles(theme);
  const navigation = useNavigation();
  const route = useRoute();

  // Cache masterData once from route params
  const [master, setMaster] = useState(route.params.masterData);

  const [phoneNumber, setPhoneNumber] = useState('');
  const [phoneVerified, setPhoneVerified] = useState(false);
  const [error, setError] = useState('');

  // State for bus number search
  const [busQuery, setBusQuery] = useState('');
  const [busNumber, setBusNumber] = useState('');
  const [busList, setBusList] = useState([]);
  const [filteredBuses, setFilteredBuses] = useState([]);

  // 1) Verify Phone Number
  useEffect(() => {
    if (phoneNumber.trim().length >= 10) {
      if (master && master.drivers && Array.isArray(master.drivers)) {
        const typedPhone = phoneNumber.replace(/\D+/g, '');
        const valid = master.drivers.includes(typedPhone);

        if (valid) {
          setPhoneVerified(true);
          setError('');
        } else {
          setPhoneVerified(false);
          setError(t('number.errors.invalidPhone'));
        }
      } else {
        setPhoneVerified(false);
        setError(t('number.errors.driverNotFound'));
      }
    } else {
      setPhoneVerified(false);
      setError('');
    }
  }, [phoneNumber, master, t]);
  // 2) Load bus list from master data and filter out active buses
  useEffect(() => {
    console.log("master data is ", master);

    const fetchAvailableBuses = async () => {
      if (master && Array.isArray(master.buses)) {
        try {
          // Get available (inactive) buses using the new function
          const availableBuses = await getAvailableBuses(master.buses);

          setBusList(availableBuses);
          setFilteredBuses(availableBuses);
        } catch (error) {
          console.error('Error fetching available buses:', error);
          // Fallback to showing all buses if there's an error
          setBusList(master.buses);
          setFilteredBuses(master.buses);
        }
      } else {
        console.log('Invalid master data structure:', master); // Debug invalid case
      }
    };

    fetchAvailableBuses();
  }, [master]);
  // 3) Filter buses as user types
  useEffect(() => {
    if (!busQuery.trim()) {
      setFilteredBuses(busList);
    } else {
      const filtered = busList.filter(bus =>
        bus.toLowerCase().includes(busQuery.toLowerCase())
      );
      setFilteredBuses(filtered);
    }
  }, [busQuery, busList]);
  // 4) Handle form submission
  const handleSubmit = async () => {
    if (phoneVerified && busNumber.trim()) {
      try {
        // Save both values in parallel
        await Promise.all([
          AsyncStorage.setItem('isLoggedIn', 'true'),
          AsyncStorage.setItem('busNumber', busNumber),  // Use busNumber instead of busQuery
          AsyncStorage.setItem('phoneNumber', phoneNumber),
        ]);

        navigation.reset({
          index: 0,
          routes: [{
            name: 'Tracking',
            params: {
              phoneNumber: phoneNumber,
              busNumber: busNumber  // Use busNumber instead of busQuery
            }
          }],
        });
      } catch (err) {
        console.error('Error saving data:', err);
      }
    }
  };  // Remove the extra closing brace and semicolon here
  // Add keyboard visibility state
  const [keyboardVisible, setKeyboardVisible] = useState(false);

  // Add keyboard listeners
  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener(
      'keyboardDidShow',
      () => {
        setKeyboardVisible(true);
      }
    );
    const keyboardDidHideListener = Keyboard.addListener(
      'keyboardDidHide',
      () => {
        setKeyboardVisible(false);
      }
    );

    // Clean up listeners
    return () => {
      keyboardDidShowListener.remove();
      keyboardDidHideListener.remove();
    };
  }, []);

  return (
    <View style={{ flex: 1, backgroundColor: theme.background }}>
      <ScrollView
        contentContainerStyle={[
          styles.scrollContainer,
          {
            backgroundColor: theme.background,
            paddingBottom: keyboardVisible ? 300 : 0,
            paddingHorizontal: 20,
            flexGrow: 1
          }
        ]}
        keyboardShouldPersistTaps="always"
        showsVerticalScrollIndicator={false}
        keyboardDismissMode="interactive"
      >
        {/* Header with Language Dropdown and Theme Toggle */}
        <View style={[
          styles.headerContainer,
          {
            marginTop: 10,
            marginBottom: keyboardVisible ? 4 : 15,
            height: keyboardVisible ? 50 : 'auto'
          }
        ]}>
          <View style={styles.themeContainer}>
            <ColorModeToggle />
          </View>

          <View style={styles.languageContainer}>
            <LanguageSelectionDropdown />
          </View>
        </View>

        {/* Logo Section - Hide when keyboard is visible */}
        {!keyboardVisible && (
          <View style={[styles.logoContainer, { marginBottom: 25 }]}>
            <View style={styles.logoBox}>
              <Image
                source={LogoImage}
                style={styles.logoImage}
                resizeMode="contain"
              />
            </View>
          </View>
        )}

        {/* Phone Number Section */}
        <View style={styles.inputContainer}>
          <Text style={[styles.label, { color: theme.text }]}>{t('number.phoneLabel')}</Text>
          <View style={[styles.phoneInputContainer, {
            backgroundColor: theme.inputBackground || theme.normal_background,
            borderColor: theme.inputBorder || theme.primary,
            minHeight: 60  // Changed to minHeight to accommodate multiline text
          }]}>
            <TextInput
              style={[styles.phoneInputField, {
                color: theme.text,
                minHeight: 60,  // Changed to minHeight
                fontSize: 18, // Slightly larger font
                textAlignVertical: 'center', // Helps with multiline text alignment
                paddingTop: 12 // Add padding for multiline text
              }]}
              placeholder={t('number.phonePlaceholder')}
              placeholderTextColor={theme.placeholderColor || '#999'}
              value={phoneNumber}
              onChangeText={(text) => {
                // Only allow digits and limit to 10 characters
                const formattedText = text.replace(/[^0-9]/g, '').slice(0, 10);
                setPhoneNumber(formattedText);
                setError('');
              }}
              keyboardType="phone-pad"
              returnKeyType="done"
              maxLength={10}
              multiline={true} // Allow text to wrap to next line
              numberOfLines={2} // Allow up to 2 lines
            />
            <LinearGradient
              colors={
                phoneVerified
                  ? [theme.success, '#7fffa5']
                  : [theme.primary, '#b0c9f5']
              }
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
              style={[styles.verifyBox, {
                minHeight: 60,  // Changed to minHeight to match the input
                justifyContent: 'center', // Center content vertically
                alignItems: 'center' // Center content horizontally
              }]}
            >
              <Text style={styles.verifyText}>
                {phoneVerified ? t('number.verifyButton') : t('number.verifyButton')}
              </Text>
            </LinearGradient>
          </View>
          {error && !phoneVerified && (
            <Text style={[styles.errorText, { color: theme.error || 'red' }]}>{error}</Text>
          )}
        </View>

        {/* Bus Number Section (Searchable) */}
        <View style={[styles.inputContainer, { marginBottom: keyboardVisible ? 20 : 0 }]}>
          <Text style={[styles.label, { color: theme.text }]}>{t('number.busLabel')}</Text>
          <TextInput
            style={[
              styles.input,
              {
                color: theme.text,
                backgroundColor: theme.inputBackground || theme.normal_background,
                borderColor: theme.inputBorder || theme.primary,
                textAlignVertical: 'center', // Helps with multiline text alignment
                paddingTop: 12, // Add padding for multiline text
                minHeight: 60 // Ensure enough height for multiline content
              },
              phoneVerified ? null : styles.disabledInput
            ]}
            placeholder={t('number.busPlaceholder')}
            placeholderTextColor={theme.placeholderColor || '#999'}
            value={busQuery}
            onChangeText={(text) => {
              setBusQuery(text);
              setBusNumber('');
            }}
            returnKeyType="done"
            editable={phoneVerified}
            multiline={true} // Allow text to wrap to next line
            numberOfLines={2} // Allow up to 2 lines
          />

          {busQuery.trim().length > 0 && filteredBuses.length > 0 && (
            <View style={[styles.suggestionContainer, {
              maxHeight: Platform.OS === "ios" ? 200 : 180,
              elevation: 5,
              shadowColor: theme.text
            }]}>
              <ScrollView
                style={styles.suggestionList}
                nestedScrollEnabled={true}
                keyboardShouldPersistTaps="always"
              >
                {filteredBuses.map((bus, index) => (
                  <TouchableOpacity
                    key={index}
                    style={[styles.suggestionItemContainer, {
                      backgroundColor: theme.inputBackground
                    }]}
                    onPress={() => {
                      setBusQuery(bus);
                      setBusNumber(bus);
                      // Don't dismiss keyboard until user is done with selection
                      // Keyboard.dismiss(); - Removing this line

                      // Only hide the dropdown after selection
                      setFilteredBuses([]);
                    }}
                  >
                    <Text style={[styles.suggestionItem, { color: theme.text }]}>{bus}</Text>
                    <Text style={[styles.suggestionArrow, { color: theme.primary }]}>{'>'}</Text>
                  </TouchableOpacity>
                ))}
              </ScrollView>
            </View>
          )}
        </View>

        {/* Next Button */}
        <View style={styles.buttonContainer}>
          <TouchableOpacity
            onPress={handleSubmit}
            disabled={!(phoneVerified && busNumber.trim())}
            activeOpacity={0.8}
          >
            <LinearGradient
              colors={theme.buttonGradient}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
              style={[
                styles.button,
                { opacity: phoneVerified && busNumber.trim() ? 1 : 0.5 },
              ]}
            >
              <Text style={styles.buttonText}>{t('number.startButton')}</Text>
            </LinearGradient>
          </TouchableOpacity>
        </View>
      </ScrollView>
      {/* </TouchableWithoutFeedback> */}
      {/* </KeyboardAvoidingView> */}
      {/* Removed KeyboardAvoidingView closing tag */}
    </View>
  );
};

export default NumberScreen;
