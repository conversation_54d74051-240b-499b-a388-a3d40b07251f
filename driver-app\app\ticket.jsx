import React, { useState, useContext } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Alert
} from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { ThemeContext } from '../contexts/ThemeContext';
import { useTranslation } from 'react-i18next';
import FontAwesome6 from '@expo/vector-icons/FontAwesome6';
import ColorModeToggle from '../components/ColorModeToggle';
import createStyles from './Style_Schema/ticket_schema';

export default function TicketScreen() {
  const { theme } = useContext(ThemeContext);
  const styles = createStyles(theme);
  const { t } = useTranslation();
  const navigation = useNavigation();
  const route = useRoute();

  // State for form data
  const [fareAmount, setFareAmount] = useState('0.00');
  const [fromCity, setFromCity] = useState('');
  const [toCity, setToCity] = useState('');
  const [selectedPassengerType, setSelectedPassengerType] = useState('Adult');

  // Cities data - you can expand this based on your routes
  const cities = [
    'Mayiladuthurai',
    'Karaikal',
    'Ambagarhur',
    'Nellankudi',
    'Sengur'
  ];

  // Quick action stops
  const quickStops = [
    'Ambagarhur',
    'Nellankudi',
    'Sengur',
    'Ambagarhur' // Duplicate as shown in your image
  ];

  const handleBackPress = () => {
    navigation.goBack();
  };

  const handleQuickStopPress = (stopName) => {
    setToCity(stopName);
  };

  const handlePassengerTypePress = (type) => {
    setSelectedPassengerType(type);
  };

  const handlePrintPress = () => {
    Alert.alert(t('ticket.alerts.printTitle'), t('ticket.alerts.printMessage'));
  };

  const handleLuggagePress = () => {
    Alert.alert(t('ticket.alerts.luggageTitle'), t('ticket.alerts.luggageMessage'));
  };

  const handleCancelPress = () => {
    Alert.alert(
      t('ticket.alerts.cancelTitle'),
      t('ticket.alerts.cancelMessage'),
      [
        { text: t('common.no'), style: 'cancel' },
        { text: t('common.yes'), onPress: handleBackPress }
      ]
    );
  };

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={handleBackPress} style={styles.backButton}>
          <FontAwesome6 name="chevron-left" size={24} color={theme.textColor} />
        </TouchableOpacity>
        <ColorModeToggle />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Fare Amount */}
        <View style={styles.fareContainer}>
          <Text style={styles.fareLabel}>{t('ticket.fareAmount')} :</Text>
          <Text style={styles.fareAmount}>{fareAmount} RS</Text>
        </View>

        {/* From Dropdown */}
        <View style={styles.dropdownContainer}>
          <Text style={styles.dropdownLabel}>{t('ticket.fromLabel')}</Text>
          <TouchableOpacity style={styles.dropdown}>
            <Text style={styles.dropdownText}>
              {fromCity || t('ticket.selectDeparture')}
            </Text>
            <FontAwesome6 name="chevron-down" size={16} color={theme.textColor} />
          </TouchableOpacity>
        </View>

        {/* To Dropdown */}
        <View style={styles.dropdownContainer}>
          <Text style={styles.dropdownLabel}>{t('ticket.toLabel')}</Text>
          <TouchableOpacity style={styles.dropdown}>
            <Text style={styles.dropdownText}>
              {toCity || t('ticket.selectDestination')}
            </Text>
            <FontAwesome6 name="chevron-down" size={16} color={theme.textColor} />
          </TouchableOpacity>
        </View>

        {/* Quick Action Buttons */}
        <View style={styles.quickActionsContainer}>
          {quickStops.map((stop, index) => (
            <TouchableOpacity
              key={`${stop}-${index}`}
              style={styles.quickActionButton}
              onPress={() => handleQuickStopPress(stop)}
            >
              <Text style={styles.quickActionText}>{stop}</Text>
            </TouchableOpacity>
          ))}
        </View>

        {/* Passenger Type Buttons */}
        <View style={styles.passengerTypeContainer}>
          {[
            { key: 'Child', label: t('ticket.passengerTypes.child') },
            { key: 'Adult', label: t('ticket.passengerTypes.adult') },
            { key: 'Bus Pass', label: t('ticket.passengerTypes.busPass') }
          ].map((type) => (
            <TouchableOpacity
              key={type.key}
              style={[
                styles.passengerTypeButton,
                selectedPassengerType === type.key && styles.passengerTypeButtonSelected
              ]}
              onPress={() => handlePassengerTypePress(type.key)}
            >
              <Text style={[
                styles.passengerTypeText,
                selectedPassengerType === type.key && styles.passengerTypeTextSelected
              ]}>
                {type.label}
              </Text>
            </TouchableOpacity>
          ))}
        </View>

        {/* Action Buttons */}
        <View style={styles.actionButtonsContainer}>
          <TouchableOpacity style={styles.printButton} onPress={handlePrintPress}>
            <Text style={styles.printButtonText}>{t('ticket.buttons.print')}</Text>
          </TouchableOpacity>

          <View style={styles.bottomButtonsRow}>
            <TouchableOpacity style={styles.luggageButton} onPress={handleLuggagePress}>
              <Text style={styles.luggageButtonText}>{t('ticket.buttons.luggage')}</Text>
            </TouchableOpacity>

            <TouchableOpacity style={styles.cancelButton} onPress={handleCancelPress}>
              <Text style={styles.cancelButtonText}>{t('ticket.buttons.cancel')}</Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </View>
  );
}
