import React, { useContext } from 'react';
import { View, Text, Modal, StyleSheet, TouchableOpacity, BackHandler, Platform } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';
import { ThemeContext } from '../contexts/ThemeContext';

const NoInternetModal = ({ visible }) => {
  const { t } = useTranslation();
  const { theme, mode } = useContext(ThemeContext);
  
  const handleExitApp = () => {
    if (Platform.OS === 'android') {
      BackHandler.exitApp();
    }
  };

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="fade"
      onRequestClose={() => {}}
    >
      <View style={styles.modalContainer}>
        <View style={[styles.modalContent, { backgroundColor: theme.secondaryBackground }]}>
          <Ionicons name="wifi-off" size={50} color="#FF6347" style={styles.icon} />
          <Text style={[styles.modalTitle, { color: theme.text }]}>
            {t('network.noConnectionTitle')}
          </Text>
          <Text style={[styles.modalMessage, { color: theme.text }]}>
            {t('network.noConnectionMessage')}
          </Text>
          <View style={styles.buttonContainer}>
            <TouchableOpacity 
              style={[
                styles.button, 
                styles.exitButton,
                { borderWidth: 1, borderColor: mode === 'dark' ? '#ffffff50' : '#00000030' }
              ]} 
              onPress={handleExitApp}
            >
              <Text style={styles.buttonText}>{t('network.exitButton')}</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    borderRadius: 10,
    padding: 20,
    width: '80%',
    alignItems: 'center',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  icon: {
    marginBottom: 15,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
    textAlign: 'center',
  },
  modalMessage: {
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 20,
  },
  buttonContainer: {
    width: '100%',
  },
  button: {
    padding: 12,
    borderRadius: 5,
    alignItems: 'center',
    justifyContent: 'center',
  },
  exitButton: {
    backgroundColor: '#FF6347',
  },
  buttonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 16,
  },
});

export default NoInternetModal;