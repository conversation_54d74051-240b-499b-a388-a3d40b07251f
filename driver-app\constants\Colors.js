// Colors.js

// MainColors remain constant across themes.
export const mainColors = {
  primary: '#6777E8',
  secondary: '#00ADB5',   
  accent: '#FFC107',

  //INDEPENDANT OF THEME/COLOR MODE
  normal_text: '#FFFFFF',
  normal_background: '#FFFFFF', 
  normal_shadow: '#000000',   
  normal_borderColor: '#000000',     
  normal_placeholderColor: '#adadad', 

  buttonGradient: ['#6777E8', '#95B2EC'],

  success: '#3de369',
  
  XIconColor: '#FF0000', //in tracking page
};

// Overrides for Light Mode:
export const lightMode = {
  text: '#000000',       // Text color: black
  background: '#FFFFFF', // Background color: white
  secondaryBackground: mainColors.normal_background,
  shadow: '#000000',     // Shadow color: black
  borderColor: '#000000',     // Border color: black
  // borderColor: mainColors.primary,     // Border color: black

  statusBarColor: '#FFFFFF',

  busIconColor: mainColors.primary,

  // Button-specific colors:
  startButtonColor: ['#6777E8', '#8A9BF8'],
  startButtonTextColor: '#FFFFFF',

  redirectButton: '#FFFFFF',
  redirectTextColor: '#000000',
  delayedButton: '#000000', 
  delayedTextColor: '#FFFFFF',
};

// Overrides for Dark Mode:
export const darkMode = {
  text: '#FFFFFF',       // Text color: white
  background: '#00040F', // Background color: black
  secondaryBackground: '#1C1C1C', 
  shadow: '#FFFFFF',     // Shadow color: white
  borderColor: '#FFFFFF',     // Border color: white

  statusBarColor: '#171717',

  busIconColor: '#FFFFFF',

  // Button-specific colors:
  startButtonColor: ['#FFFFFF', '#b3b3b3'],
  startButtonTextColor: '#000000',

  delayedButton: '#FFFFFF',
  delayedTextColor: '#000000',
  delayedButtonBorderColor: '#000000',   
  redirectButton: '#000000',
  redirectTextColor: '#FFFFFF',
  redirectButtonBorderColor: '#FFFFFF',   
};
