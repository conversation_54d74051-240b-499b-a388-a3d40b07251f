import { StyleSheet } from 'react-native';

const createStyles = (theme) =>
  StyleSheet.create({
    container: {
      flex: 1,
      paddingHorizontal: 20,
      paddingVertical: 40,
      backgroundColor: theme.background,
    },
    // Header container for theme toggle and language dropdown
    headerContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 20,
      width: '100%',
    },
    // Theme toggle container style
    themeContainer: {
      flexDirection: 'row',
      justifyContent: 'flex-start',
      zIndex: 10,
    },
    // Language dropdown container
    languageContainer: {
      zIndex: 10,
    },
    logoContainer: {
      alignItems: 'center',
      marginBottom: 50,
    },
    logoBox: {
      width: 300,
      height: 300,
      // backgroundColor: theme.normal_background,
      borderRadius: 20,
      justifyContent: 'center',
      alignItems: 'center',
      // shadowColor: theme.normal_shadow,
      // shadowOpacity: 0.1,
      // shadowOffset: { width: 0, height: 2 },
      // shadowRadius: 4,
      // elevation: 3,
    },
    logoImage: {
      width: '80%',
      height: '80%',
    },
    logoText: {
      color: theme.text,
      fontSize: 24,
      fontWeight: 'bold',
    },
    inputContainer: {
      marginBottom: 10,
    },
    label: {
      fontSize: 20,
      fontWeight: '600',
      marginBottom: 8,
      // color is applied inline
    },
    input: {
      borderRadius: 20,
      borderWidth: 1,
      paddingHorizontal: 16,
      paddingVertical: 17,
      fontSize: 18,
      marginBottom: 10,
      shadowColor: theme.normal_shadow,
      shadowOpacity: 0.5,
      shadowOffset: { width: 0, height: 1 },
      shadowRadius: 2,
      elevation: 2,
      // color, backgroundColor and borderColor are applied inline
    },
    errorText: {
      fontSize: 14,
      marginBottom: 10,
      // color is applied inline
    },
    helpText: {
      fontSize: 14,
      textAlign: 'center',
      // color is applied inline
    },
    scrollContainer: {
      flexGrow: 1,
      justifyContent: 'center',
      // paddingBottom: 20,
    },
    buttonContainer: {
      marginTop: 'auto',
      paddingBottom: 20,
    },
    button: {
      borderRadius: 30,
      paddingVertical: 17,
      alignItems: 'center',
    },
    buttonText: {
      color: theme.buttonTextColor || '#FFFFFF',
      fontSize: 20,
      fontWeight: '600',
    },
  });

export default createStyles;
