import React, { useState, useEffect } from 'react';
import { View, Text, Button, StyleSheet, Alert, ActivityIndicator } from 'react-native';
import * as Updates from 'expo-updates';

const UpdateChecker = () => {
  const [checking, setChecking] = useState(false);
  const [updateAvailable, setUpdateAvailable] = useState(false);

  // Check for updates when component mounts
  useEffect(() => {
    checkForUpdates(false); // Silent check on startup
  }, []);

  const checkForUpdates = async (showNoUpdateAlert = true) => {
    try {
      setChecking(true);
      const update = await Updates.checkForUpdateAsync();
      
      if (update.isAvailable) {
        setUpdateAvailable(true);
        Alert.alert(
          "Update Available",
          "A new version is available. Would you like to update now?",
          [
            { text: "Later", style: "cancel" },
            {
              text: "Update",
              onPress: async () => {
                try {
                  await Updates.fetchUpdateAsync();
                  Alert.alert(
                    "Update Downloaded",
                    "The update has been downloaded. The app will now restart to apply the changes.",
                    [{ text: "OK", onPress: () => Updates.reloadAsync() }]
                  );
                } catch (error) {
                  Alert.alert("Error", "Failed to download the update: " + error.message);
                  console.error(error);
                }
              }
            }
          ]
        );
      } else {
        setUpdateAvailable(false);
        if (showNoUpdateAlert) {
          Alert.alert("No Updates", "You're running the latest version.");
        }
      }
    } catch (error) {
      if (showNoUpdateAlert) {
        Alert.alert("Error", "Failed to check for updates: " + error.message);
      }
      console.error(error);
    } finally {
      setChecking(false);
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>App Updates</Text>
      <Text style={styles.info}>Branch: production</Text>
      {updateAvailable && (
        <Text style={styles.updateAvailable}>New update available!</Text>
      )}
      <Button 
        title={checking ? "Checking..." : "Check for Updates"} 
        onPress={() => checkForUpdates(true)} 
        disabled={checking}
      />
      {checking && <ActivityIndicator style={styles.loader} />}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
    backgroundColor: '#f0f0f0',
    borderRadius: 8,
    margin: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  info: {
    marginBottom: 16,
    color: '#666',
  },
  updateAvailable: {
    color: 'green',
    fontWeight: 'bold',
    marginBottom: 10,
  },
  loader: {
    marginTop: 10,
  }
});

export default UpdateChecker;