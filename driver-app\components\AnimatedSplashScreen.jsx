// Remove the unused BlurView import
import React, { useEffect, useContext } from 'react';
import { View, Text, StyleSheet, Image, Dimensions } from 'react-native';
import Animated, { 
  useSharedValue, 
  useAnimatedStyle, 
  withTiming, 
  withSequence, 
  withDelay,
  Easing,
  runOnJS
} from 'react-native-reanimated';
import { LinearGradient } from 'expo-linear-gradient';
import * as SplashScreen from 'expo-splash-screen';
import { ThemeContext } from '../contexts/ThemeContext';

const { width, height } = Dimensions.get('window');

const AnimatedSplashScreen = ({ onAnimationComplete }) => {
  // Get theme from context
  const { theme, mode } = useContext(ThemeContext);
  
  // Animation values
  const logoScale = useSharedValue(0);
  const logoOpacity = useSharedValue(1);
  const textOpacity = useSharedValue(0);
  const waveScale = useSharedValue(0);
  const waveOpacity = useSharedValue(0.7);
  const bgOpacity = useSharedValue(0);
  const logoRotate = useSharedValue(0);
  
  // Logo animation style
  const logoAnimatedStyle = useAnimatedStyle(() => {
    return {
      transform: [
        { scale: logoScale.value },
        { rotateY: `${logoRotate.value}deg` }
      ],
      opacity: logoOpacity.value,
    };
  });
  
  // Text animation style
  const textAnimatedStyle = useAnimatedStyle(() => {
    return {
      opacity: textOpacity.value,
      transform: [{ translateY: (1 - textOpacity.value) * 20 }]
    };
  });
  
  // Wave animation style
  const waveAnimatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: waveScale.value }],
      opacity: waveOpacity.value,
    };
  });
  
  // Background animation style
  const bgAnimatedStyle = useAnimatedStyle(() => {
    return {
      opacity: bgOpacity.value,
    };
  });

  // Start animations when component mounts
  useEffect(() => {
    // Hide the native splash screen with a slight delay
    setTimeout(() => {
      SplashScreen.hideAsync().catch(error => {
        console.log("Error hiding splash screen:", error);
      });
      
      // Background fade in
      bgOpacity.value = withTiming(1, { duration: 800 });
      
      // Logo animation with subtle 3D effect
      logoRotate.value = withSequence(
        withTiming(10, { duration: 600 }),
        withTiming(-5, { duration: 400 }),
        withTiming(0, { duration: 300 })
      );
      
      // Logo scale animation
      logoScale.value = withSequence(
        withTiming(1.05, { duration: 800, easing: Easing.out(Easing.back(1.2)) }),
        withTiming(1, { duration: 400, easing: Easing.inOut(Easing.ease) })
      );
      
      // Text animation - starts after logo animation
      textOpacity.value = withDelay(
        700, 
        withTiming(1, { duration: 800, easing: Easing.inOut(Easing.ease) })
      );
      
      // Wave animation - pulsing effect
      waveScale.value = withDelay(
        400,
        withSequence(
          withTiming(1.2, { duration: 1200 }),
          withTiming(2.0, { duration: 1200 }),
          withTiming(2.8, { duration: 800, easing: Easing.out(Easing.ease) }, (finished) => {
            if (finished) {
              // When animation completes, notify parent component
              runOnJS(onAnimationComplete)();
            }
          })
        )
      );
      
      waveOpacity.value = withDelay(
        400,
        withSequence(
          withTiming(0.5, { duration: 1200 }),
          withTiming(0.3, { duration: 1200 }),
          withTiming(0, { duration: 800 })
        )
      );
    }, 300);
  }, []);

  // Get gradient colors based on theme
  const gradientColors = mode === 'dark' 
    ? ['#1A1A1A', '#000000'] 
    : ['#5A6AE8', '#3F51B5'];  // Slightly adjusted blue for better contrast

  return (
    <View style={[styles.container, { backgroundColor: mode === 'dark' ? '#000000' : '#5A6AE8' }]}>
      <Animated.View style={[styles.fullScreen, bgAnimatedStyle]}>
        <LinearGradient
          colors={gradientColors}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={styles.background}
        />
        
        {/* Decorative elements */}
        <View style={styles.decorContainer}>
          {mode === 'dark' ? (
            <>
              <View style={[styles.decorCircle, { top: height * 0.1, left: width * 0.1, backgroundColor: 'rgba(103, 119, 232, 0.15)' }]} />
              <View style={[styles.decorCircle, { bottom: height * 0.15, right: width * 0.15, backgroundColor: 'rgba(103, 119, 232, 0.2)' }]} />
              <View style={[styles.decorLine, { top: height * 0.3, right: width * 0.2 }]} />
              <View style={[styles.decorLine, { bottom: height * 0.3, left: width * 0.2, transform: [{ rotate: '45deg' }] }]} />
              <View style={[styles.decorDot, { top: height * 0.2, right: width * 0.3 }]} />
              <View style={[styles.decorDot, { bottom: height * 0.4, left: width * 0.3 }]} />
            </>
          ) : (
            <>
              <View style={[styles.decorCircle, { top: height * 0.1, left: width * 0.1, backgroundColor: 'rgba(255, 255, 255, 0.2)' }]} />
              <View style={[styles.decorCircle, { bottom: height * 0.15, right: width * 0.15, backgroundColor: 'rgba(255, 255, 255, 0.25)' }]} />
              <View style={[styles.decorLine, { top: height * 0.3, right: width * 0.2 }]} />
              <View style={[styles.decorLine, { bottom: height * 0.3, left: width * 0.2, transform: [{ rotate: '45deg' }] }]} />
              <View style={[styles.decorDot, { top: height * 0.2, right: width * 0.3 }]} />
              <View style={[styles.decorDot, { bottom: height * 0.4, left: width * 0.3 }]} />
            </>
          )}
        </View>
      </Animated.View>
      
      {/* Multiple animated wave circles for better effect */}
      <Animated.View style={[styles.wave, waveAnimatedStyle, { 
        backgroundColor: mode === 'dark' ? 'rgba(103, 119, 232, 0.25)' : 'rgba(255, 255, 255, 0.35)' 
      }]} />
      <Animated.View style={[styles.wave2, waveAnimatedStyle, { 
        backgroundColor: mode === 'dark' ? 'rgba(103, 119, 232, 0.2)' : 'rgba(255, 255, 255, 0.25)' 
      }]} />
      <Animated.View style={[styles.wave3, waveAnimatedStyle, { 
        backgroundColor: mode === 'dark' ? 'rgba(103, 119, 232, 0.15)' : 'rgba(255, 255, 255, 0.15)' 
      }]} />
      
      {/* Logo */}
      <Animated.View style={[styles.logoContainer, logoAnimatedStyle]}>
        <Image 
          source={require('../assets/graphics/busbeatpilotlogo.png')} 
          style={styles.logo}
          resizeMode="contain"
        />
      </Animated.View>
      
      {/* App name */}
      <Animated.View style={[styles.textContainer, textAnimatedStyle]}>
        <Text style={[styles.appName, { 
          color: '#FFFFFF',
          textShadowColor: 'rgba(0, 0, 0, 0.5)',
          textShadowOffset: { width: 1, height: 1 },
          textShadowRadius: 4
        }]}>BusBeat Pilot</Text>
        <Text style={[styles.tagline, { 
          color: mode === 'dark' ? 'rgba(255, 255, 255, 0.8)' : 'rgba(255, 255, 255, 0.9)'
        }]}>
          Track. Navigate. Simplify.
        </Text>
      </Animated.View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  fullScreen: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  background: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
  },
  decorContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  decorCircle: {
    position: 'absolute',
    width: 120,
    height: 120,
    borderRadius: 60,
  },
  decorDot: {
    position: 'absolute',
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: 'rgba(255, 255, 255, 0.4)',
  },
  logoContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 20,
  },
  logo: {
    width: 180,
    height: 180,
  },
  textContainer: {
    alignItems: 'center',
    marginTop: 20,
    zIndex: 10,
  },
  appName: {
    fontSize: 38,
    fontWeight: 'bold',
    marginBottom: 8,
    letterSpacing: 1.2,
  },
  tagline: {
    fontSize: 16,
    letterSpacing: 0.5,
  },
  decorLine: {
    position: 'absolute',
    width: 80,
    height: 4,
    backgroundColor: 'rgba(103, 119, 232, 0.2)',
    borderRadius: 2,
  },
  wave: {
    position: 'absolute',
    width: 150,
    height: 150,
    borderRadius: 75,
    zIndex: 5,
  },
  wave2: {
    position: 'absolute',
    width: 200,
    height: 200,
    borderRadius: 100,
    zIndex: 5,
  },
  wave3: {
    position: 'absolute',
    width: 250,
    height: 250,
    borderRadius: 125,
    zIndex: 5,
  },
});

export default AnimatedSplashScreen;