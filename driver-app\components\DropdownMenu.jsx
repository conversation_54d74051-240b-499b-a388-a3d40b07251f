import React, { useState, useEffect, useRef } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Animated } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useContext } from 'react';
import { ThemeContext } from '../contexts/ThemeContext';

// Update the component definition
const DropdownMenu = ({
  isOpen,
  onToggle,
  onUpdatePress,
  onFuelDataPress,
  onSettingsPress,
  onProfilePress
}) => {
  const { theme } = useContext(ThemeContext);
  // Use the external state instead of internal state
  const fadeAnim = useRef(new Animated.Value(isOpen ? 1 : 0)).current;
  const scaleAnim = useRef(new Animated.Value(isOpen ? 1 : 0.8)).current;

  useEffect(() => {
    if (isOpen) {
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 200,
          useNativeDriver: true,
        }),
        Animated.timing(scaleAnim, {
          toValue: 1,
          duration: 200,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 0,
          duration: 150,
          useNativeDriver: true,
        }),
        Animated.timing(scaleAnim, {
          toValue: 0.8,
          duration: 150,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [isOpen, fadeAnim, scaleAnim]);

  const toggleDropdown = () => {
    onToggle(!isOpen);
  };

  const handleItemPress = (callback) => {
    onToggle(false);
    if (callback) callback();
  };

  return (
    <>
      {/* We'll handle the overlay in the parent component */}
      <View style={styles.container}>
        <TouchableOpacity
          style={[
            styles.menuButton,
            { backgroundColor: theme.secondaryBackground || 'rgba(255,255,255,0.2)' }
          ]}
          onPress={toggleDropdown}
        >
          <MaterialIcons name="more-vert" size={24} color={theme.text} />
        </TouchableOpacity>

        {isOpen && (
          <Animated.View
            style={[
              styles.dropdownMenu,
              {
                backgroundColor: theme.secondaryBackground || theme.cardBackground || '#FFFFFF',
                opacity: fadeAnim,
                transform: [{ scale: scaleAnim }],
              }
            ]}
          >
            <TouchableOpacity
              style={[
                styles.dropdownItem,
                { borderBottomColor: theme.borderColor || 'rgba(0,0,0,0.1)' }
              ]}
              onPress={() => handleItemPress(onUpdatePress)}
            >
              <MaterialIcons name="update" size={22} color={theme.text} />
              <Text style={[styles.dropdownText, { color: theme.text }]}>Release Notes</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.dropdownItem,
                { borderBottomColor: theme.borderColor || 'rgba(0,0,0,0.1)' }
              ]}
              onPress={() => handleItemPress(onFuelDataPress)}
            >
              <MaterialIcons name="local-gas-station" size={22} color={theme.text} />
              <Text style={[styles.dropdownText, { color: theme.text }]}>Fuel Data</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.dropdownItem,
                { borderBottomColor: theme.borderColor || 'rgba(0,0,0,0.1)' }
              ]}
              onPress={() => handleItemPress(onSettingsPress)}
            >
              <MaterialIcons name="settings" size={22} color={theme.text} />
              <Text style={[styles.dropdownText, { color: theme.text }]}>Settings</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.dropdownItem}
              onPress={() => handleItemPress(onProfilePress)}
            >
              <MaterialIcons name="person" size={22} color={theme.text} />
              <Text style={[styles.dropdownText, { color: theme.text }]}>Profile</Text>
            </TouchableOpacity>
          </Animated.View>
        )}
      </View>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 24,
    right: 20,
    zIndex: 1000,
  },
  menuButton: {
    padding: 8,
    borderRadius: 20,
    zIndex: 1001,
  },
  dropdownMenu: {
    position: 'absolute',
    top: 45,
    right: 0,
    width: 180,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.1)',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 6,
    elevation: 8,
    zIndex: 1000,
    overflow: 'hidden',
  },
  dropdownItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 14,
    borderBottomWidth: 1,
  },
  dropdownText: {
    marginLeft: 12,
    fontSize: 15,
    fontWeight: '500',
  },
});

export default DropdownMenu;