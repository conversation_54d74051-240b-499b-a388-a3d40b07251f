import React, { useContext } from 'react';
import { TouchableOpacity, StyleSheet, View, Keyboard } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { ThemeContext } from '../contexts/ThemeContext';

const ColorModeToggle = () => {
  const { mode, toggleTheme, theme } = useContext(ThemeContext);
  
  const handleToggle = () => {
    // Dismiss keyboard and prevent focus from shifting
    Keyboard.dismiss();
    toggleTheme();
  };
  
  return (
    <TouchableOpacity 
      onPress={handleToggle}
      style={[
        styles.toggleButton,
        { backgroundColor: mode === 'dark' ? theme.inputBackground || '#333333' : theme.normal_background,
          borderColor: mode === 'dark' ? '#777777' : theme.primary }
      ]}
      activeOpacity={0.7}
    >
      <View style={styles.iconContainer}>
        <Ionicons 
          name={mode === 'dark' ? 'moon' : 'sunny'} 
          size={20} 
          color={theme.text} 
        />
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  toggleButton: {
    borderRadius: 10,
    padding: 10,
    borderWidth: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  iconContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  }
});

export default ColorModeToggle;
