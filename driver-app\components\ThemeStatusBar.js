import React, { useContext } from 'react';
import { StatusBar } from 'react-native';
import { ThemeContext } from '../contexts/ThemeContext';

const ThemedStatusBar = () => {
  const { theme, mode } = useContext(ThemeContext);
  return (
    <StatusBar 
      barStyle={mode === 'light' ? 'dark-content' : 'light-content'} 
      backgroundColor={theme.statusBarColor} 
    />
  );
};

export default ThemedStatusBar;
