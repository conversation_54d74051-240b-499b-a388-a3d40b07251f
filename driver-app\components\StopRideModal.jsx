import React, { useState } from 'react';
import { View, Text, TouchableOpacity, Modal, StyleSheet } from 'react-native';
import { useContext } from 'react';
import { ThemeContext } from '../contexts/ThemeContext';
import { useTranslation } from 'react-i18next';
import BusNumberConfirmationModal from './BusNumberConfirmationModal';

const StopRideModal = ({ visible, onCancel, onConfirm, busNumber }) => {
  const { theme, mode } = useContext(ThemeContext);
  const { t } = useTranslation();
  const [confirmModalVisible, setConfirmModalVisible] = useState(false);
  
  const handleStopRequest = () => {
    setConfirmModalVisible(true);
  };

  const handleConfirm = () => {
    setConfirmModalVisible(false);
    onConfirm();
  };

  const styles = StyleSheet.create({
    modalOverlay: {
      flex: 1,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      justifyContent: 'center',
      alignItems: 'center',
    },
    modalContent: {
        backgroundColor: theme.secondaryBackground || '#FFFFFF',
      borderRadius: 15,
      padding: 20,
      width: '80%',
      alignItems: 'center',
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.25,
      shadowRadius: 3.84,
      elevation: 5,
      opacity: confirmModalVisible ? 0.7 : 1
    },
    modalTitle: {
      fontSize: 20,
      fontWeight: 'bold',
      color: theme.text,
      marginBottom: 15,
    },
    modalText: {
      fontSize: 16,
      color: theme.text,
      textAlign: 'center',
      marginBottom: 20,
    },
    modalButtons: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      width: '100%',
    },
    modalButton: {
      paddingVertical: 10,
      paddingHorizontal: 20,
      borderRadius: 8,
      minWidth: '40%',
      alignItems: 'center',
    },
    cancelButton: {
      backgroundColor: theme.cancelButton || '#ccc',
    },
    confirmButton: {
      backgroundColor: theme.danger || '#ff3b30',
    },
    modalButtonText: {
      color: '#fff',
      fontWeight: 'bold',
      fontSize: 16,
    },
  });

  return (
    <>
      <Modal
        animationType="fade"
        transparent={true}
        visible={visible}
        onRequestClose={onCancel}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>{t('tracking.stopModal.title')}</Text>
            <Text style={styles.modalText}>
              {t('tracking.stopModal.message')}
            </Text>
            <View style={styles.modalButtons}>
              <TouchableOpacity
                style={[styles.modalButton, styles.cancelButton]}
                onPress={onCancel}
                disabled={confirmModalVisible}
              >
                <Text style={styles.modalButtonText}>{t('common.cancel')}</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.modalButton, styles.confirmButton]}
                onPress={handleStopRequest}
                disabled={confirmModalVisible}
              >
                <Text style={styles.modalButtonText}>{t('tracking.stopModal.confirmButton')}</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* Add BusNumberConfirmationModal */}
      <BusNumberConfirmationModal
        visible={confirmModalVisible}
        onCancel={() => setConfirmModalVisible(false)}
        onConfirm={handleConfirm}
        actionType="stop"
        busNumber={busNumber}
        theme={theme}
        mode={mode}
      />
    </>
  );
};

export default StopRideModal;