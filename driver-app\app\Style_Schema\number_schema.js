import { StyleSheet } from 'react-native';

const createStyles = (theme) => ({
  container: {
    flex: 1,
    paddingHorizontal: 20,
    paddingTop: 20,
    backgroundColor: theme.background, // Added background color to match theme
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: 50,
  },
  logoBox: {
    width: 300,
    height: 300,
    // backgroundColor: theme.normal_background,
    // borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    // shadowColor: theme.normal_shadow,
    // shadowOpacity: 0.1,
    // shadowOffset: { width: 0, height: 2 },
    // shadowRadius: 4,
    // elevation: 3,
  },
  logoText: {
    color: theme.primary,
    fontSize: 18,
    fontWeight: '600',
  },
  inputContainer: {
    marginBottom: 10, // Unified marginBottom
  },
  label: {
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 8,
  },
  input: {
    backgroundColor: theme.inputBackground || theme.normal_background,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: theme.primary,
    paddingHorizontal: 16,
    paddingVertical: 17,
    fontSize: 18,         // Unified font size
    marginBottom: 10,     // Unified marginBottom
    shadowColor: theme.normal_shadow,
    shadowOpacity: 0.5,
    shadowOffset: { width: 0, height: 1 },
    shadowRadius: 2,
    elevation: 2,
    color: theme.text,
  },
  // Updated disabled input style with lighter dark gray for dark mode
  disabledInput: {
    backgroundColor: theme.mode === 'dark' ? '#ccd1d1' : '#f3f3f3',
    borderColor: theme.mode === 'dark' ? '#666666' : '#ccc',
    color: theme.mode === 'dark' ? '#999999' : '#999',
  },
  phoneInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.normal_background,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: theme.primary,
    marginBottom: 10,
    shadowColor: theme.normal_shadow,
    shadowOpacity: 0.5,
    shadowOffset: { width: 0, height: 1 },
    shadowRadius: 2,
    elevation: 2,
  },
  phoneInputField: {
    flex: 1,
    fontSize: 16,
    color: theme.text,
    paddingLeft:20
  },
  verifyBox: {
    paddingHorizontal: 30,
    paddingVertical: 19,
    borderTopRightRadius: 20,
    borderBottomRightRadius: 20,
  },
  verifyText: {
    color: theme.normal_text,
    fontSize: 17,
    fontWeight: '600',
  },
  buttonContainer: {
    marginTop: 'auto',
    paddingBottom: 20,
  },
  button: {
    borderRadius: 30,
    paddingVertical: 17,
    alignItems: 'center',
  },
  buttonText: {
    color: theme.normal_text,
    fontSize: 20,
    fontWeight: '600',
  },
  errorText: {
    marginTop: 5,
    marginLeft:20,
    color: 'red',
  },
  // Suggestions
  suggestionContainer: {
    marginTop: -5,
    marginBottom: 10,
  },
  suggestionList: {
    backgroundColor: theme.inputBackground || theme.normal_background,
    borderWidth: 1,
    borderColor: theme.primary,
    borderRadius: 10,
    maxHeight: 180,
    shadowColor: theme.normal_shadow,
    shadowOpacity: 0.2,
    shadowOffset: { width: 0, height: 2 },
    shadowRadius: 4,
    elevation: 3,
  },
  suggestionItemContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: theme.mode === 'dark' ? '#444444' : '#ccc',
  },
  suggestionItem: {
    fontSize: 18,
    color: theme.text,
  },
  suggestionArrow: {
    fontSize: 18,
    color: theme.primary,
  },
  // Add this to your existing styles
  scrollContainer: {
    flexGrow: 1,
    backgroundColor: theme.background,
    // paddingBottom: Platform.OS === "ios" ? 300 : 250
  },
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    marginBottom: 20,
  },
  themeContainer: {
    alignSelf: 'flex-start',
  },
  languageContainer: {
    alignSelf: 'flex-end',
  },
  logoImage: {
    width: '80%',
    height: '80%',
  },
});

export default createStyles;
