import * as Updates from 'expo-updates';
import { Alert } from 'react-native';
import { EventRegister } from 'react-native-event-listeners';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Constants from 'expo-constants';
import { CURRENT_RELEASE_NOTES } from '../constants/ReleaseNotes';

// Get current app version
export const getCurrentVersion = () => {
  return Constants.expoConfig?.version || '7.7.7';
};

// Format release notes for display
export const formatReleaseNotes = (releaseNotes) => {
  if (!releaseNotes || !releaseNotes.notes) {
    return "No release notes available.";
  }
  
  // Format the notes as a bulleted list
  const formattedNotes = releaseNotes.notes.map(note => `• ${note}`).join('\n');
  return formattedNotes;
};

// Only check if an update is available without fetching it
export const checkForUpdates = async (showNoUpdateAlert = true) => {
  try {
    // First check if updates are enabled in the app
    if (!Updates.isEnabled) {
      console.log("Updates are disabled in app.json");
      return false;
    }
    
    console.log("Checking for updates...");
    const update = await Updates.checkForUpdateAsync();
    
    if (update.isAvailable) {
      console.log("Update is available");
      const formattedNotes = formatReleaseNotes(CURRENT_RELEASE_NOTES);
      
      // Notify UI that an update is available
      EventRegister.emit('updateAvailable', { 
        available: true,
        notes: formattedNotes
      });
      return true;
    } else {
      console.log("No update available");
      
      if (showNoUpdateAlert) {
        // Only show "no updates" message for manual checks
        const currentVersion = getCurrentVersion();
        const formattedNotes = formatReleaseNotes(CURRENT_RELEASE_NOTES);
        
        // Clear any existing events first to prevent duplicates
        EventRegister.removeAllListeners('noUpdatesAvailable');
        
        console.log("Emitting noUpdatesAvailable with notes:", formattedNotes);
        // Use a timeout to ensure the event is processed after any UI updates
        setTimeout(() => {
          EventRegister.emit('noUpdatesAvailable', { 
            version: currentVersion,
            notes: formattedNotes
          });
        }, 100);
      }
      return false;
    }
  } catch (error) {
    console.error('Error checking for updates:', error);
    if (showNoUpdateAlert) {
      EventRegister.emit('updateError', error.message || 'Failed to check for updates');
    }
    return false;
  }
};

// Only fetch and apply update when explicitly called by user action
export const fetchAndApplyUpdate = async () => {
  try {
    // Show downloading progress
    EventRegister.emit('updateDownloading', true);
    
    console.log("Fetching update...");
    const { isNew } = await Updates.fetchUpdateAsync();

    if (isNew) {
      console.log("Update downloaded successfully");
      
      // Wait a moment for the progress animation to complete
      setTimeout(() => {
        EventRegister.emit('updateDownloading', false);
        
        // Instead of reloading immediately, emit an event that the update is ready
        EventRegister.emit('updateDownloaded', true);
      }, 1000);
    } else {
      console.log("No new update was downloaded");
      EventRegister.emit('updateDownloading', false);
      
      const currentVersion = getCurrentVersion();
      const formattedNotes = formatReleaseNotes(CURRENT_RELEASE_NOTES);
      
      EventRegister.emit('noUpdatesAvailable', { 
        version: currentVersion,
        notes: formattedNotes
      });
    }
  } catch (error) {
    console.error("Error fetching update:", error);
    EventRegister.emit('updateDownloading', false);
    EventRegister.emit('updateError', error.message || "Failed to download update");
  }
};