import React from 'react';
import { View, Text, Modal, TouchableOpacity, StyleSheet, ScrollView } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { ThemeContext } from '../contexts/ThemeContext';
import { LinearGradient } from 'expo-linear-gradient';
import { useTranslation } from 'react-i18next';

const ReleaseNotesModal = ({ visible, onClose, version, date, notes }) => {
  const { theme, mode } = React.useContext(ThemeContext);
  const { t } = useTranslation();

  return (
    <Modal
      transparent={true}
      animationType="fade"
      visible={visible}
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <View style={[
          styles.modalContainer, 
          { 
            backgroundColor: theme.secondaryBackground,
            borderColor: mode === 'dark' ? theme.text : 'transparent',
            borderWidth: mode === 'dark' ? 1 : 0,
          }
        ]}>
           <View style={styles.headerRow}>
            <MaterialIcons 
              name="notes" 
              size={30} 
              color={mode === 'dark' ? '#FFFFFF' : theme.primary} 
              style={styles.icon} 
            />
            <Text style={[styles.title, { color: theme.text }]}>Release Notes</Text>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <MaterialIcons name="close" size={24} color={theme.text} />
            </TouchableOpacity>
          </View>
          
          <View style={styles.versionContainer}>
            <Text style={[styles.versionText, { color: theme.text }]}>
              Version {version}
            </Text>
            {date && (
              <Text style={[styles.dateText, { color: theme.text, opacity: 0.7 }]}>
                Released: {date}
              </Text>
            )}
          </View>
          
          <ScrollView style={styles.notesScrollView}>
            <Text style={[styles.notesText, { color: theme.text }]}>
              {notes}
            </Text>
          </ScrollView>
          
          <LinearGradient
            colors={mode === 'dark' ? theme.startButtonColor : theme.buttonGradient}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
            style={styles.closeButtonBottom}
          >
            <TouchableOpacity 
              style={styles.closeButtonInner}
              onPress={onClose}
            >
              <Text style={[
                styles.closeButtonText, 
                { color: mode === 'dark' ? theme.startButtonTextColor : theme.normal_text }
              ]}>
                {t('tracking.releaseNotesModal.closeButton')}
              </Text>
            </TouchableOpacity>
          </LinearGradient>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modalContainer: {
    width: '90%',
    maxHeight: '80%',
    borderRadius: 15,
    padding: 20,
    elevation: 5,
  },
  headerRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
  },
  icon: {
    marginRight: 10,
  },
  title: {
    fontSize: 22,
    fontWeight: 'bold',
    flex: 1,
  },
  closeButton: {
    padding: 5,
  },
  versionContainer: {
    marginBottom: 15,
  },
  versionText: {
    fontSize: 16,
    fontWeight: '500',
  },
  dateText: {
    fontSize: 14,
    marginTop: 2,
  },
  notesScrollView: {
    maxHeight: 300,
    marginBottom: 20,
  },
  notesText: {
    fontSize: 16,
    lineHeight: 24,
  },
  closeButtonBottom: {
    borderRadius: 10,
    overflow: 'hidden',
  },
  closeButtonInner: {
    width: '100%',
    padding: 12,
    alignItems: 'center',
  },
  closeButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default ReleaseNotesModal;