import React, { useEffect } from 'react';
import { View, Text, Modal, StyleSheet, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';
import { useContext } from 'react';
import { ThemeContext } from '../contexts/ThemeContext';

const ConnectionRestoredModal = ({ visible, onClose, autoCloseTime = 3000 }) => {
  const { t } = useTranslation();
  const { theme, mode } = useContext(ThemeContext);
  
  // Auto-close the modal after specified time
  useEffect(() => {
    if (visible) {
      const timer = setTimeout(() => {
        onClose();
      }, autoCloseTime);
      
      return () => clearTimeout(timer);
    }
  }, [visible, onClose, autoCloseTime]);

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="fade"
      onRequestClose={onClose}
    >
      <View style={styles.modalContainer}>
        <View style={[styles.modalContent, { backgroundColor: theme.secondaryBackground }]}>
          <Ionicons name="wifi" size={50} color="#4CAF50" style={styles.icon} />
          <Text style={[styles.modalTitle, { color: theme.text }]}>
            {t('network.connectionRestoredTitle', 'Connection Restored')}
          </Text>
          <Text style={[styles.modalMessage, { color: theme.text }]}>
            {t('network.connectionRestoredMessage', 'Refreshing data to ensure everything is up to date.')}
          </Text>
          <TouchableOpacity 
            style={[
              styles.button, 
              { 
                backgroundColor: theme.primaryColor,
                borderWidth: 1,
                borderColor: mode === 'dark' ? '#ffffff50' : '#00000030'
              }
            ]} 
            onPress={onClose}
          >
            <Text style={[styles.buttonText, { color: mode === 'dark' ? 'white' : 'black' }]}>
              {t('common.ok', 'OK')}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    borderRadius: 10,
    padding: 20,
    width: '80%',
    alignItems: 'center',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  icon: {
    marginBottom: 15,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
    textAlign: 'center',
  },
  modalMessage: {
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 20,
  },
  button: {
    paddingVertical: 12,
    paddingHorizontal: 30,
    borderRadius: 5,
    alignItems: 'center',
    justifyContent: 'center',
  },
  buttonText: {
    fontWeight: 'bold',
    fontSize: 16,
  },
});

export default ConnectionRestoredModal;