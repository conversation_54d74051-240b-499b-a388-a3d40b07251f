/* eslint-disable */
import * as Router from 'expo-router';

export * from 'expo-router';

declare module 'expo-router' {
  export namespace ExpoRouter {
    export interface __routes<T extends string | object = string> {
      hrefInputParams: { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/`; params?: Router.UnknownInputParams; } | { pathname: `/number`; params?: Router.UnknownInputParams; } | { pathname: `/ticket`; params?: Router.UnknownInputParams; } | { pathname: `/tracking`; params?: Router.UnknownInputParams; } | { pathname: `/uid`; params?: Router.UnknownInputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; } | { pathname: `/Style_Schema/number_schema`; params?: Router.UnknownInputParams; } | { pathname: `/Style_Schema/ticket_schema`; params?: Router.UnknownInputParams; } | { pathname: `/Style_Schema/tracking_schema`; params?: Router.UnknownInputParams; } | { pathname: `/Style_Schema/uid_schema`; params?: Router.UnknownInputParams; };
      hrefOutputParams: { pathname: Router.RelativePathString, params?: Router.UnknownOutputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownOutputParams } | { pathname: `/`; params?: Router.UnknownOutputParams; } | { pathname: `/number`; params?: Router.UnknownOutputParams; } | { pathname: `/ticket`; params?: Router.UnknownOutputParams; } | { pathname: `/tracking`; params?: Router.UnknownOutputParams; } | { pathname: `/uid`; params?: Router.UnknownOutputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownOutputParams; } | { pathname: `/Style_Schema/number_schema`; params?: Router.UnknownOutputParams; } | { pathname: `/Style_Schema/ticket_schema`; params?: Router.UnknownOutputParams; } | { pathname: `/Style_Schema/tracking_schema`; params?: Router.UnknownOutputParams; } | { pathname: `/Style_Schema/uid_schema`; params?: Router.UnknownOutputParams; };
      href: Router.RelativePathString | Router.ExternalPathString | `/${`?${string}` | `#${string}` | ''}` | `/number${`?${string}` | `#${string}` | ''}` | `/ticket${`?${string}` | `#${string}` | ''}` | `/tracking${`?${string}` | `#${string}` | ''}` | `/uid${`?${string}` | `#${string}` | ''}` | `/_sitemap${`?${string}` | `#${string}` | ''}` | `/Style_Schema/number_schema${`?${string}` | `#${string}` | ''}` | `/Style_Schema/ticket_schema${`?${string}` | `#${string}` | ''}` | `/Style_Schema/tracking_schema${`?${string}` | `#${string}` | ''}` | `/Style_Schema/uid_schema${`?${string}` | `#${string}` | ''}` | { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/`; params?: Router.UnknownInputParams; } | { pathname: `/number`; params?: Router.UnknownInputParams; } | { pathname: `/ticket`; params?: Router.UnknownInputParams; } | { pathname: `/tracking`; params?: Router.UnknownInputParams; } | { pathname: `/uid`; params?: Router.UnknownInputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; } | { pathname: `/Style_Schema/number_schema`; params?: Router.UnknownInputParams; } | { pathname: `/Style_Schema/ticket_schema`; params?: Router.UnknownInputParams; } | { pathname: `/Style_Schema/tracking_schema`; params?: Router.UnknownInputParams; } | { pathname: `/Style_Schema/uid_schema`; params?: Router.UnknownInputParams; };
    }
  }
}
