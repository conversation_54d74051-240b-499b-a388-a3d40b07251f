import { create } from 'zustand';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { getBusDataWithStops } from '../scripts/DynamoData';

// Create a Zustand store with persistence
const useBusStore = create((set, get) => ({
  // Bus data state
  busData: null,
  busStops: [],
  isLoading: false,
  error: null,
  
  // Initialize the store with data from AsyncStorage
  initializeStore: async (busNumber) => {
    set({ isLoading: true, error: null });
    
    try {
      // First check if we have cached data in AsyncStorage
      const cachedData = await AsyncStorage.getItem(`bus_data_${busNumber}`);
      console.log("cachedData: ", cachedData);
      
      if (cachedData) {
        // If we have cached data, use it
        const parsedData = JSON.parse(cachedData);
        set({ 
          busData: parsedData.busData, 
          busStops: parsedData.busStops,
          isLoading: false 
        });
        
        // Return true to indicate we're using cached data
        return true;
      }
      
      // If no cached data, fetch from DynamoDB
      return await get().fetchBusData(busNumber);
    } catch (error) {
      console.error('Error initializing bus store:', error);
      set({ error: error.message, isLoading: false });
      return false;
    }
  },
  
  // Fetch bus data from DynamoDB
  fetchBusData: async (busNumber) => {
    set({ isLoading: true, error: null });
    
    try {
      // Fetch bus data with stops from DynamoDB
      const { busData, busStops } = await getBusDataWithStops(busNumber);
      
      if (!busData) {
        throw new Error(`No data found for bus ${busNumber}`);
      }
      
      // Store in Zustand state
      set({ busData, busStops, isLoading: false });
      
      // Cache in AsyncStorage
      await AsyncStorage.setItem(
        `bus_data_${busNumber}`, 
        JSON.stringify({ busData, busStops, timestamp: Date.now() })
      );
      
      return true;
    } catch (error) {
      console.error('Error fetching bus data:', error);
      set({ error: error.message, isLoading: false });
      return false;
    }
  },
  
  // Clear the store data
  clearStore: async (busNumber) => {
    set({ busData: null, busStops: [], error: null });
    
    try {
      // Remove from AsyncStorage
      await AsyncStorage.removeItem(`bus_data_${busNumber}`);
    } catch (error) {
      console.error('Error clearing bus store:', error);
    }
  }
}));

export default useBusStore;