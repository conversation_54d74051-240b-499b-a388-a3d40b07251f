import React, { useState } from 'react';
import { View, Text, Modal, TouchableOpacity, StyleSheet, TextInput } from 'react-native';
import { useTranslation } from 'react-i18next';

const BusNumberConfirmationModal = ({ 
  visible, 
  onCancel, 
  onConfirm, 
  actionType, 
  busNumber, 
  theme, 
  mode 
}) => {
  const { t } = useTranslation();
  const [enteredBusNumber, setEnteredBusNumber] = useState('');
  const [error, setError] = useState('');

  const handleConfirm = () => {
    if (enteredBusNumber.trim() === busNumber) {
      setEnteredBusNumber('');
      setError('');
      onConfirm();
    } else {
      setError(t('tracking.lastStopModal.busNumberMismatch'));
    }
  };

  return (
    <Modal
      animationType="fade"
      transparent={true}
      visible={visible}
      onRequestClose={onCancel}
    >
      <View style={styles.modalContainer}>
        <View style={[
          styles.modalContent,
          { 
            backgroundColor: theme.secondaryBackground,
            borderColor: mode === 'dark' ? theme.text : 'transparent',
            borderWidth: mode === 'dark' ? 1 : 0,
          }
        ]}>
          <Text style={[styles.modalTitle, { color: theme.text }]}>
            {actionType === 'end' 
              ? t('tracking.lastStopModal.confirmEndTitle') 
              : actionType === 'start'
                ? t('tracking.lastStopModal.confirmStartTitle')
                : t('tracking.stopModal.confirmStopTitle')}
          </Text>
          
          <Text style={[styles.modalText, { color: theme.text }]}>
            {t('tracking.lastStopModal.enterBusNumber')}
          </Text>
          
          <View style={styles.busNumberContainer}>
            <Text style={[styles.busNumberLabel, { color: theme.text }]}>
              {t('tracking.lastStopModal.currentBusNumber')}: 
            </Text>
            <Text style={[styles.busNumberValue, { color: theme.primary || '#4CAF50' }]}>
              {busNumber}
            </Text>
          </View>
          
          <TextInput
            style={[styles.input, { 
              borderColor: theme.borderColor || '#ccc',
              color: theme.text,
              backgroundColor: theme.inputBackground || 'transparent',
              textAlign: 'center',
              textAlignVertical: 'center'
            }]}
            value={enteredBusNumber}
            onChangeText={setEnteredBusNumber}
            placeholder={t('tracking.lastStopModal.busNumberPlaceholder')}
            placeholderTextColor={theme.placeholderText || '#999'}
            keyboardType="default"
            multiline={true}
            numberOfLines={2}
          />
          
          {error ? <Text style={styles.errorText}>{error}</Text> : null}
          
          <View style={styles.confirmButtons}>
            <TouchableOpacity 
              style={[styles.confirmButton, styles.cancelButton]} 
              onPress={onCancel}
            >
              <Text style={styles.confirmButtonText}>{t('common.cancel')}</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={[styles.confirmButton, styles.confirmActionButton]} 
              onPress={handleConfirm}
            >
              <Text style={styles.confirmButtonText}>{t('common.confirm')}</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    paddingHorizontal: 10,
    paddingVertical: 40, // Increased vertical padding
  },
  modalContent: {
    borderRadius: 15,
    padding: 22, // Increased padding
    width: '100%',
    maxWidth: 340, // Set a max width for better appearance
    alignItems: 'center',
    elevation: 8, // Increased elevation
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 6,
  },
  modalTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    marginBottom: 15,
    textAlign: 'center',
  },
  modalText: {
    fontSize: 16,
    marginBottom: 15,
    textAlign: 'center',
    lineHeight: 22,
  },
  busNumberContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 15,
  },
  busNumberLabel: {
    fontSize: 14,
    fontWeight: '500',
    marginRight: 5,
  },
  busNumberValue: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  input: {
    width: '100%',
    minHeight: 50,
    height: 'auto',
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 15,
    paddingVertical: 10,
    marginBottom: 15,
    fontSize: 16,
  },
  errorText: {
    color: '#f44336',
    marginBottom: 15,
    textAlign: 'center',
  },
  confirmButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
  },
  confirmButton: {
    flex: 1,
    paddingHorizontal: 10,
    paddingVertical: 20,
    borderRadius: 8,
    alignItems: 'center',
    marginHorizontal: 5,
  },
  cancelButton: {
    backgroundColor: '#757575',
  },
  confirmActionButton: {
    backgroundColor: '#4CAF50',
  },
  confirmButtonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 12,
  },
});

export default BusNumberConfirmationModal;