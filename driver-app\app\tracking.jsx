import React, { useState, useEffect, useRef, useContext } from 'react';
import { Animated, View, Text, TouchableOpacity, ImageBackground, Alert, AppState } from 'react-native';
import FontAwesome6 from '@expo/vector-icons/FontAwesome6';
import { MaterialIcons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { Easing } from 'react-native';
import createStyles from './Style_Schema/tracking_schema';
import MapsImage from '../assets/graphics/maps.png';
import MapsImageDark from '../assets/graphics/maps-dark.png';
import ColorModeToggle from '../components/ColorModeToggle';
import LogoutButton from '../components/LogoutButton';
import { ThemeContext } from '../contexts/ThemeContext';
import { useRoute } from '@react-navigation/native';
import * as TaskManager from 'expo-task-manager';
import * as Location from 'expo-location';
import * as Updates from 'expo-updates';
import * as Notifications from 'expo-notifications';
import AsyncStorage from '@react-native-async-storage/async-storage';
import axios from 'axios';
import {
  updateBusLocation,
  updateBusStatus,
  updateDriverStatus,
  clearBusHistory,
  getBusHistory,
  startNewJourney,
  endJourney,
  getBusCurrentCoordinates,  // Add this import
  clearBusCoordinates,        // Add this import
  updateDriverAssignedBus,
  updateBusDelayStatus
} from '../scripts/DynamoData';
import { EventRegister } from 'react-native-event-listeners';
import StopRideModal from '../components/StopRideModal';
// Add import for the ResumeRideModal component
import ResumeRideModal from '../components/ResumeRideModal';
import LastStopModal from '../components/LastStopModal';
import DropdownMenu from '../components/DropdownMenu';
import { checkForUpdates, getCurrentVersion } from '../utils/updateUtils';
import ReleaseNotesModal from '../components/ReleaseNotesModal';
import { CURRENT_RELEASE_NOTES } from '../constants/ReleaseNotes';
import { useTranslation } from 'react-i18next';
import useBusStore from '../store/busStore';
import Constants from 'expo-constants';

// Add these variables outside the component for tracking delay
let lastCoordinates = null;
let stationaryStartTime = null;
let delayCheckInterval = null;
const DELAY_THRESHOLD_METERS = 10;
const DELAY_THRESHOLD_MINUTES = 10;

// Background location task
TaskManager.defineTask('BACKGROUND_LOCATION_TASK', async ({ data, error }) => {
  if (error) {
    console.error('Location task error:', error);
    // If there's an error with the location task, set bus and driver to inactive
    try {
      const busNumber = await AsyncStorage.getItem('busNumber');
      const phoneNumber = await AsyncStorage.getItem('phoneNumber');

      if (busNumber && phoneNumber) {
        console.log(`Setting bus ${busNumber} and driver ${phoneNumber} to inactive due to location task error`);
        await updateDriverStatus(phoneNumber, 'Inactive');
        await updateBusStatus(busNumber, 'Inactive');
      }
    } catch (setInactiveError) {
      console.error('Error setting inactive status on location task error:', setInactiveError);
    }
    return;
  }

  if (data) {
    const { locations } = data;
    if (locations && locations.length > 0) {
      const coordinates = {
        latitude: locations[0].coords.latitude,
        longitude: locations[0].coords.longitude
      };
      console.log('BACKGROUND_LOCATION_TASK received coordinates:', coordinates);
      // Get busNumber from AsyncStorage instead of expecting it in the data object
      try {
        const busNumber = await AsyncStorage.getItem('busNumber');
        if (busNumber) {
          // Try to get cached bus stops from AsyncStorage
          let cachedBusStops = null;
          try {
            const cachedData = await AsyncStorage.getItem(`bus_data_${busNumber}`);
            // console.log("111:",cachedData);

            if (cachedData) {
              const parsedData = JSON.parse(cachedData);
              cachedBusStops = parsedData.busStops;
              console.log(`Retrieved cached bus stops for bus ${busNumber}, count:`, cachedBusStops?.length || 0)
            }
          } catch (cacheError) {
            console.error('Error retrieving cached bus stops:', cacheError);
          }
          // Update the bus location in DynamoDB and get the result
          // Pass the cached bus stops to avoid fetching them again
          console.log(`Updating location for bus ${busNumber}:`, coordinates);
          const result = await updateBusLocation(busNumber, coordinates, cachedBusStops);
          // Add more logging for debugging
          console.log(`Update result for bus ${busNumber}:`, result);
          // Check if bus is stationary (delayed)
          checkBusDelay(busNumber, coordinates);

          // If this is the last stop, trigger an event that the app can listen for
          if (result && result.isLastStop) {
            console.log(`Emitting lastStopReached event for bus ${busNumber}`);
            EventRegister.emit('lastStopReached', busNumber);
          }
        } else {
          console.error('Bus number not found in AsyncStorage');
        }
      } catch (error) {
        console.error('Error retrieving bus number:', error);
      }
    }
  }
});
// Add this function to check if the bus is delayed
const checkBusDelay = async (busNumber, currentCoordinates) => {
  // Calculate distance if we have previous coordinates
  if (lastCoordinates) {
    const distance = calculateDistance(
      lastCoordinates.latitude,
      lastCoordinates.longitude,
      currentCoordinates.latitude,
      currentCoordinates.longitude
    );

    console.log(`Bus ${busNumber} moved ${distance.toFixed(2)} meters since last check`);

    // If bus moved more than threshold, reset the stationary timer
    if (distance > DELAY_THRESHOLD_METERS) {
      console.log(`Bus ${busNumber} moved more than ${DELAY_THRESHOLD_METERS} meters, resetting delay timer`);
      stationaryStartTime = null;

      // If the bus was previously marked as delayed, update to not delayed
      try {
        await updateBusDelayStatus(busNumber, false);
      } catch (error) {
        console.error('Error updating bus delay status to false:', error);
      }
    }
    // If bus is within threshold distance and timer not started, start the timer
    else if (!stationaryStartTime) {
      console.log(`Bus ${busNumber} is stationary, starting delay timer`);
      stationaryStartTime = new Date();
    }
    // If bus is within threshold and timer is running, check if delay threshold is reached
    else {
      const currentTime = new Date();
      const stationaryDuration = (currentTime - stationaryStartTime) / (1000 * 60); // in minutes

      console.log(`Bus ${busNumber} has been stationary for ${stationaryDuration.toFixed(2)} minutes`);

      // If stationary for more than threshold minutes, mark as delayed
      if (stationaryDuration >= DELAY_THRESHOLD_MINUTES) {
        console.log(`Bus ${busNumber} is delayed (stationary for ${stationaryDuration.toFixed(2)} minutes)`);
        try {
          await updateBusDelayStatus(busNumber, true);
        } catch (error) {
          console.error('Error updating bus delay status to true:', error);
        }
      }
    }
  }

  // Update last coordinates
  lastCoordinates = { ...currentCoordinates };
};
// Add this function to calculate distance between two points using Haversine formula
const calculateDistance = (lat1, lon1, lat2, lon2) => {
  const R = 6371; // Earth's radius in kilometers
  const dLat = (lat2 - lat1) * Math.PI / 180;
  const dLon = (lon2 - lon1) * Math.PI / 180;
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
    Math.sin(dLon / 2) * Math.sin(dLon / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  return R * c * 1000; // Convert to meters
};
// Add a task completed listener to detect when the location task is stopped
// TaskManager.isTaskRegisteredAsync('BACKGROUND_LOCATION_TASK').then(isRegistered => {
//   if (isRegistered) {
//     TaskManager.unregisterTaskAsync('BACKGROUND_LOCATION_TASK')
//       .catch(error => {
//         console.log('Failed to unregister task:', error);
//       });
//   }
// });

// Add a task unregistration handler
TaskManager.defineTask('TASK_UNREGISTRATION_HANDLER', async () => {
  try {
    const busNumber = await AsyncStorage.getItem('busNumber');
    const phoneNumber = await AsyncStorage.getItem('phoneNumber');

    if (busNumber && phoneNumber) {
      console.log(`Setting bus ${busNumber} and driver ${phoneNumber} to inactive on task unregistration`);
      await updateDriverStatus(phoneNumber, 'Inactive');
      await updateBusStatus(busNumber, 'Inactive');
    }
  } catch (error) {
    console.error('Error setting inactive status on task unregistration:', error);
  }
});
// Configure notifications
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: false,
    shouldSetBadge: false,
  }),
});

export default function TrackingScreen() {
  const { theme, mode } = useContext(ThemeContext);
  const styles = createStyles(theme);
  const { t, i18n } = useTranslation();
  const route = useRoute();
  const busNumber = route.params?.busNumber;

  const [isStarted, setIsStarted] = useState(false);
  const [waves, setWaves] = useState([]);

  const pressAnim = useRef(new Animated.Value(1)).current;
  const [isPressing, setIsPressing] = useState(false);
  // Add state for modal visibility
  const [showStopModal, setShowStopModal] = useState(false);
  // Add state for resume modal visibility
  const [showResumeModal, setShowResumeModal] = useState(false);
  // Add state for tracking current coordinates
  const [hasCurrentCoordinates, setHasCurrentCoordinates] = useState(false);
  // Add state for tracking if bus has history
  const [hasHistory, setHasHistory] = useState(false);
  // Add notification ID state to track active notification
  const [notificationId, setNotificationId] = useState(null);
  // Add state for last stop modal visibility
  const [showLastStopModal, setShowLastStopModal] = useState(false);
  // Add this state to track dropdown open state
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  // Add AppState reference to track app state changes
  const appState = useRef(AppState.currentState);
  const [showReleaseNotesModal, setShowReleaseNotesModal] = useState(false);
  const [releaseNotesVersion, setReleaseNotesVersion] = useState('');
  const [releaseNotesDate, setReleaseNotesDate] = useState('');
  const [releaseNotesContent, setReleaseNotesContent] = useState('');
  // Add this near your other state variables
  const [locationWatcher, setLocationWatcher] = useState(null);
  const {
    busData,
    busStops,
    isLoading,
    error,
    initializeStore,
    clearStore
  } = useBusStore();
  // Add loading state for bus data
  const [isBusDataLoading, setIsBusDataLoading] = useState(true);
  // Add loading indicator animation value
  const loadingAnimation = useRef(new Animated.Value(0)).current;
  // Create loading animation
  useEffect(() => {
    if (isBusDataLoading) {
      Animated.loop(
        Animated.sequence([
          Animated.timing(loadingAnimation, {
            toValue: 1,
            duration: 800,
            easing: Easing.linear,
            useNativeDriver: true
          }),
          Animated.timing(loadingAnimation, {
            toValue: 0,
            duration: 800,
            easing: Easing.linear,
            useNativeDriver: true
          })
        ])
      ).start();
    } else {
      loadingAnimation.stopAnimation();
    }

    return () => loadingAnimation.stopAnimation();
  }, [isBusDataLoading]);

  useEffect(() => {
    const loadBusData = async () => {
      if (busNumber) {
        setIsBusDataLoading(true);
        try {
          const success = await initializeStore(busNumber);
          // console.log("Busdata :", busData);
          // console.log("BusStopsdata :", busStops);

          if (!success) {
            console.error(`Failed to load data for bus ${busNumber}`);
          }
        } catch (error) {
          console.error('Error loading bus data:', error);
        } finally {
          setIsBusDataLoading(false);
        }
      }
    };

    loadBusData();

    // Clean up when component unmounts
    // return () => {
    //   if (busNumber) {
    //     // No need to await this since we're unmounting
    //     clearStore(busNumber);
    //     console.log("cleared store");

    //   }
    // };
  }, [busNumber]);

  useEffect(() => {
    const releaseNotesListener = EventRegister.addEventListener(
      'showReleaseNotes',
      (data) => {
        console.log("Show release notes event received:", data);
        setReleaseNotesVersion(data.version || '1.0.0');
        setReleaseNotesDate(data.date || '');
        setReleaseNotesContent(data.notes || 'No release notes available.');
        setShowReleaseNotesModal(true);
      }
    );

    // Clean up listener on unmount
    return () => {
      EventRegister.removeEventListener(releaseNotesListener);
    };
  }, []);
  // Add this function to dynamically determine font size based on language
  const getButtonTextStyle = () => {
    const currentLanguage = i18n.language;
    const baseStyle = styles.startButtonText;

    // Create a new style object with adjusted font size for specific languages
    const adjustedStyle = {
      ...baseStyle,
      fontSize: currentLanguage === 'ta' ? 20 : 32, // Smaller font for Tamil
    };

    return adjustedStyle;
  };
  // Add this function to handle dropdown state changes
  const handleDropdownToggle = (isOpen) => {
    setIsDropdownOpen(isOpen);
  };
  const unpressedShadow = {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 4,
  };

  const pressedShadow = {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.4,
    shadowRadius: 8,
    elevation: 6,
  };

  const onPressIn = () => {
    if (isStarted) return;
    setIsPressing(true);
    Animated.timing(pressAnim, {
      toValue: 0.9,
      duration: 100,
      useNativeDriver: true,
    }).start();
  };

  const onPressOut = () => {
    if (isStarted) return;
    Animated.sequence([
      Animated.timing(pressAnim, {
        toValue: 1.02,
        duration: 50,
        useNativeDriver: true,
      }),
      Animated.timing(pressAnim, {
        toValue: 1,
        duration: 50,
        useNativeDriver: true,
      }),
    ]).start(() => setIsPressing(false));
  };
  // Check for existing history or current coordinates when component mounts
  useEffect(() => {
    const checkBusHistory = async () => {
      try {
        if (busNumber) {
          // Get bus history from DynamoDB
          const busData = await getBusHistory(busNumber);
          const hasHistoryData = busData && busData.history && busData.history.length > 0;
          // Store history state
          setHasHistory(hasHistoryData);
          // Check if there are current coordinates for this bus
          const currentCoords = await getBusCurrentCoordinates(busNumber);
          const hasCoords = currentCoords &&
            currentCoords.latitude &&
            currentCoords.longitude;

          // Set flag if current coordinates exist
          setHasCurrentCoordinates(hasCoords);

          // Show resume modal if:
          // 1. We have history (meaning we've reached stops) OR
          // 2. We have current coordinates but no history (journey just started)
          if (hasHistory || hasCoords) {
            setShowResumeModal(true);
          }
        }
      } catch (error) {
        console.error('Error checking bus history or coordinates:', error);
      }
    };

    checkBusHistory();

    // Listen for the lastStopReached event
    const lastStopListener = EventRegister.addEventListener('lastStopReached', (receivedBusNumber) => {
      console.log(`Received lastStopReached event for bus ${receivedBusNumber}, current bus: ${busNumber}`);

      // Verify this is for our bus
      if (receivedBusNumber === busNumber) {
        console.log('Showing LastStopModal');
        setShowLastStopModal(true);
      }
    });

    // Clean up the event listener when component unmounts
    return () => {
      EventRegister.removeEventListener(lastStopListener);
    };
  }, [busNumber]);

  // Handle resuming the ride
  const handleResumeRide = () => {
    setShowResumeModal(false);
    // Start tracking and set bus as active
    setIsStarted(true);
    startTracking();

    // Update driver and bus status to Active
    const updateStatus = async () => {
      try {
        const phoneNumber = route.params?.phoneNumber || await AsyncStorage.getItem('phoneNumber');

        if (phoneNumber && busNumber) {
          await updateDriverStatus(phoneNumber, 'Active');
          await updateBusStatus(busNumber, 'Active');
        }
      } catch (error) {
        console.error('Error updating status:', error);
      }
    };

    updateStatus();
  };

  // Handle starting a new ride
  const handleNewRide = async () => {
    setShowResumeModal(false);

    try {
      // Clear the bus history before starting a new ride
      // Clear the bus history before starting a new ride
      if (busNumber) {
        await clearBusHistory(busNumber);
        // Also clear current coordinates - use clearBusCoordinates instead
        await clearBusCoordinates(busNumber);
        console.log('Bus history and coordinates cleared for new ride');
      }
    } catch (error) {
      console.error('Error clearing bus history or coordinates:', error);
    }
  };


  useEffect(() => {
    let interval = null;
    if (isStarted) {
      interval = setInterval(() => {
        // Use a more unique identifier by combining timestamp with a random number
        setWaves(prev => [...prev, `${Date.now()}-${Math.random().toString(36).substr(2, 5)}`]);
      }, 1500);
    }
    return () => {
      if (interval) clearInterval(interval);
      // Don't clear coordinates when app is closed
      stopTracking(false);
    };
  }, [isStarted]);

  const removeWave = key => {
    setWaves(prev => prev.filter(w => w !== key));
  };
  // // Function to schedule ongoing ride notification
  // const scheduleOngoingRideNotification = async () => {

  //   // Cancel any existing notification first
  //   if (notificationId) {
  //     await Notifications.cancelScheduledNotificationAsync(notificationId);
  //   }

  //   // Request notification permissions
  //   const { status } = await Notifications.requestPermissionsAsync();
  //   if (status !== 'granted') {
  //     // console.log('Notification permission not granted');
  //     return;
  //   }

  //   // Schedule the persistent notification
  //   const id = await Notifications.scheduleNotificationAsync({
  //     content: {
  //       title: "Ride in Progress",
  //       body: `Bus ${busNumber} is currently active and tracking location.`,
  //       data: { busNumber },
  //       sticky: true, // Make notification persistent
  //       autoDismiss: false,
  //     },
  //     trigger: null, // Show immediately
  //   });

  //   setNotificationId(id);
  // };

  // // Function to cancel the notification
  // const cancelOngoingRideNotification = async () => {
  //   // console.log("Notification cancellation started");

  //   // Store the ID in a local variable to prevent race conditions
  //   const currentNotificationId = notificationId;

  //   if (currentNotificationId) {
  //     try {
  //       await Notifications.cancelScheduledNotificationAsync(currentNotificationId);
  //       // console.log(`Successfully canceled notification with ID: ${currentNotificationId}`);
  //       setNotificationId(null);
  //       return true;
  //     } catch (error) {
  //       console.error(`Failed to cancel notification with ID ${currentNotificationId}:`, error);
  //       // Still set notificationId to null to prevent further attempts with invalid ID
  //       setNotificationId(null);
  //       return false;
  //     }
  //   } else {
  //     // Try to get all scheduled notifications and cancel them
  //     try {
  //       const allNotifications = await Notifications.getAllScheduledNotificationsAsync();
  //       // console.log(`Found ${allNotifications.length} scheduled notifications`);

  //       // Cancel all notifications related to ride tracking
  //       for (const notification of allNotifications) {
  //         if (notification.content && notification.content.title === "Ride in Progress") {
  //           await Notifications.cancelScheduledNotificationAsync(notification.identifier);
  //           // console.log(`Canceled notification with ID: ${notification.identifier}`);
  //         }
  //       }
  //     } catch (err) {
  //       console.error("Error cleaning up all notifications:", err);
  //     }

  //     // console.log("No specific notification ID found to cancel");
  //     return true; // No notification to cancel is still a success
  //   }
  // };


  const handleStopPress = () => {
    setShowStopModal(true);
  };

  const confirmStopRide = async () => {
    setIsStarted(false);
    setWaves([]);

    // Cancel notification first to ensure it's always removed
    // await cancelOngoingRideNotification();

    // Then stop tracking and clear coordinates (true = clear coordinates)
    await stopTracking(true);

    // Update driver and bus status to Inactive
    try {
      const phoneNumber = await AsyncStorage.getItem('phoneNumber');

      if (phoneNumber && busNumber) {
        await updateDriverStatus(phoneNumber, 'Inactive');
        await updateBusStatus(busNumber, 'Inactive');
        // Reset bus delay status to false
        await updateBusDelayStatus(busNumber, false);
        // Clear the bus history and current coordinates
        await clearBusHistory(busNumber);
      }
      // Explicitly clear coordinates again to ensure they're cleared
      await clearBusCoordinates(busNumber);
      console.log('Bus coordinates explicitly cleared in confirmStopRide');
      // Close the resume modal if it's open
      setShowResumeModal(false);
    } catch (error) {
      console.error('Error updating status or clearing history:', error);
    }
  };

  // Inside the TrackingScreen component, modify the startTracking function:

  const startTracking = async () => {
    try {
      // Reset delay tracking variables
      lastCoordinates = null;
      stationaryStartTime = null;

      // Log the bus number from route params
      console.log('Bus number from route params:', busNumber);
      // First check if the task is already registered
      const isTaskRegistered = await TaskManager.isTaskRegisteredAsync('BACKGROUND_LOCATION_TASK');
      if (isTaskRegistered) {
        console.log('Location task already registered, stopping it first');
        await Location.stopLocationUpdatesAsync('BACKGROUND_LOCATION_TASK');
      }

      // Request permissions with better error handling
      console.log('Requesting foreground location permissions...');
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert(t('tracking.alerts.permissionDeniedTitle'), t('tracking.alerts.enableLocationPermission'));
        return;
      }
      console.log('Foreground location permission granted');

      console.log('Requesting background location permissions...');
      const { status: bgStatus } = await Location.requestBackgroundPermissionsAsync();
      if (bgStatus !== 'granted') {
        console.log('Background location permission denied');
        Alert.alert(t('tracking.alerts.permissionDeniedTitle'), t('tracking.alerts.enableBackgroundPermission'));
        return;
      }
      console.log('Background location permission granted');
      // Store busNumber in AsyncStorage before starting location updates
      if (busNumber) {
        await AsyncStorage.setItem('busNumber', busNumber);
        console.log('Bus number stored in AsyncStorage:', busNumber);

        // Verify it was stored correctly
        const storedBusNumber = await AsyncStorage.getItem('busNumber');
        console.log('Verified bus number from AsyncStorage:', storedBusNumber);
      } else {
        console.error('Bus number is undefined or null, cannot store in AsyncStorage');
      }

      // Get phone number from AsyncStorage with verification
      const phoneNumber = await AsyncStorage.getItem('phoneNumber');
      console.log('Phone number from AsyncStorage:', phoneNumber);

      if (!phoneNumber) {
        console.error('Phone number not found in AsyncStorage');
        // Try to get it from route params as fallback
        const routePhoneNumber = route.params?.phoneNumber;
        if (routePhoneNumber) {
          console.log('Using phone number from route params:', routePhoneNumber);
          await AsyncStorage.setItem('phoneNumber', routePhoneNumber);
          console.log('Phone number stored in AsyncStorage from route params');
        } else {
          console.error('No phone number available from any source');
        }
      }

      // Final verification of both values
      const finalBusNumber = await AsyncStorage.getItem('busNumber');
      const finalPhoneNumber = await AsyncStorage.getItem('phoneNumber');

      console.log('Final verification - Bus number:', finalBusNumber);
      console.log('Final verification - Phone number:', finalPhoneNumber);

      // Assign bus to driver in DynamoDB only if both values are available
      if (finalPhoneNumber && finalBusNumber) {
        try {
          console.log(`Attempting to update driver status: ${finalPhoneNumber} to Active`);
          const driverResult = await updateDriverStatus(finalPhoneNumber, 'Active');
          console.log('Driver status update result:', driverResult);

          console.log(`Attempting to update bus status: ${finalBusNumber} to Active`);
          const busResult = await updateBusStatus(finalBusNumber, 'Active');
          console.log('Bus status update result:', busResult);

          console.log(`Attempting to assign bus ${finalBusNumber} to driver ${finalPhoneNumber}`);
          await updateDriverAssignedBus(finalPhoneNumber, finalBusNumber);
          console.log(`Bus ${finalBusNumber} assigned to driver ${finalPhoneNumber}`);
        } catch (assignError) {
          console.error('Error updating status or assigning bus to driver:', assignError);
        }
      } else {
        console.error('Cannot update status: Missing bus number or phone number');
        Alert.alert(t('tracking.alerts.errorTitle'), t('tracking.alerts.missingInfo'));
      }
      // Get current location once before starting background updates
      console.log('Getting current location...');
      const currentLocation = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.High
      });

      console.log('Current location:', currentLocation);

      // Update location immediately
      if (finalBusNumber && currentLocation) {
        const coordinates = {
          latitude: currentLocation.coords.latitude,
          longitude: currentLocation.coords.longitude
        };
        console.log('Updating initial location:', coordinates);
        await updateBusLocation(finalBusNumber, coordinates);
      }

      // Start location tracking with improved options
      console.log('Starting background location updates...');
      // Start location tracking
      await Location.startLocationUpdatesAsync('BACKGROUND_LOCATION_TASK', {
        accuracy: Location.Accuracy.High,
        // timeInterval: 10000,
        timeInterval: 4000,  // Reduced from 10000 to get more frequent updates
        distanceInterval: 5, // Changed from 10 to 0 to update regardless of distance moved
        deferredUpdatesInterval: 0, // Ensure immediate updates
        deferredUpdatesDistance: 0, // Ensure immediate updates
        showsBackgroundLocationIndicator: true,
        foregroundService: {
          notificationTitle: t('tracking.notification.title'),
          notificationBody: t('tracking.notification.body', { busNumber: busNumber }),
          notificationColor: "#FF0000"
        }
      });
      console.log('Background location updates started successfully');
      // If the bus was previously marked as delayed, reset it
      try {
        await updateBusDelayStatus(finalBusNumber, false);
        console.log('Reset delay status for bus', finalBusNumber);
      } catch (error) {
        console.error('Error resetting bus delay status:', error);
      }
      // Schedule the ongoing ride notification
      // await scheduleOngoingRideNotification();
      // Add a fallback for Expo Go
      // Check if we're running in Expo Go by checking the environment
      const isExpoGo = Constants.executionEnvironment === 'storeClient';
      if (isExpoGo) {
        console.log('Running in Expo Go environment - adding foreground location fallback');
        // Start a foreground location watcher as fallback for Expo Go
        const locationSubscription = Location.watchPositionAsync(
          {
            accuracy: Location.Accuracy.High,
            timeInterval: 4000,
            distanceInterval: 5,
          },
          async (location) => {
            try {
              const coordinates = {
                latitude: location.coords.latitude,
                longitude: location.coords.longitude
              };
              console.log('Foreground location update:', coordinates);
              const busNumber = await AsyncStorage.getItem('busNumber');
              if (busNumber) {
                await updateBusLocation(busNumber, coordinates);
              }
            } catch (error) {
              console.error('Error in foreground location watcher:', error);
            }
          }
        );

        // Store the subscription for cleanup
        setLocationWatcher(locationSubscription);
      }
    } catch (error) {
      console.log('Error starting location tracking', error);
      Alert.alert(t('tracking.alerts.errorTitle'), t('tracking.alerts.unableToStartTracking'));
    }
  };

  const stopTracking = async (clearCoords = false) => {
    console.log("Stopping tracking process started");
    // Reset delay tracking variables
    lastCoordinates = null;
    stationaryStartTime = null;
    // Clean up location watcher if it exists
    if (locationWatcher) {
      console.log('Removing foreground location watcher');
      (await locationWatcher).remove();
      setLocationWatcher(null);
    }
    // First try to stop location updates
    let locationStopped = false;
    try {
      // Check if the task exists before trying to stop it
      const tasks = await TaskManager.getRegisteredTasksAsync();
      const taskExists = tasks.some(task => task.taskName === 'BACKGROUND_LOCATION_TASK');

      if (taskExists) {
        await Location.stopLocationUpdatesAsync('BACKGROUND_LOCATION_TASK');
        console.log('Location tracking successfully stopped');
        locationStopped = true;
      } else {
        console.log('No active location tracking task found - already stopped');
        locationStopped = true; // Consider it stopped
      }

      // Only clear coordinates if explicitly requested
      if (clearCoords && busNumber) {
        await clearBusCoordinates(busNumber);
        console.log('Bus coordinates cleared as requested');
      } else {
        console.log('Keeping bus coordinates for resume functionality');
      }
    } catch (error) {
      // Check if the error is specifically about the task not being found
      if (error.message && error.message.includes("Task 'BACKGROUND_LOCATION_TASK' not found")) {
        locationStopped = true;
      } else {
        console.error('Error stopping location tracking:', error);
      }
      locationStopped = true; // Consider it stopped even if there's an error
    }

    // Try multiple approaches to cancel notifications
    try {
      // First try to cancel the specific notification
      if (notificationId) {
        await Notifications.cancelScheduledNotificationAsync(notificationId);
        // console.log(`Tried canceling specific notification with ID: ${notificationId}`);
      }

      // Then try to dismiss all notifications
      await Notifications.dismissAllNotificationsAsync();
      // console.log('Dismissed all notifications');

      // Also cancel all scheduled notifications
      await Notifications.cancelAllScheduledNotificationsAsync();
      // console.log('Canceled all scheduled notifications');

      // Reset notification ID
      setNotificationId(null);
    } catch (error) {
      console.error('Error canceling notifications:', error);
    }
    // If we're stopping tracking, make sure to reset the delay status
    try {
      if (busNumber) {
        await updateBusDelayStatus(busNumber, false);
      }
    } catch (error) {
      console.error('Error resetting bus delay status on stop:', error);
    }
    return locationStopped;
  };
  // Clean up notification when component unmounts
  // useEffect(() => {
  //   return () => {
  //     cancelOngoingRideNotification();
  //   };
  // }, []);



  // Handle "End Journey" button in last stop modal - modified to clear history
  const handleLastStopEndJourney = async () => {
    try {
      if (busNumber) {

        setIsStarted(false);
        setWaves([]);
        // await cancelOngoingRideNotification();
        await stopTracking(true);

        // Update driver status to Inactive
        const phoneNumber = await AsyncStorage.getItem('phoneNumber');
        if (phoneNumber) {
          await updateDriverStatus(phoneNumber, 'Inactive');
        }
        // Explicitly clear bus history
        await clearBusHistory(busNumber);

        // Also update bus status to Inactive
        await updateBusStatus(busNumber, 'Inactive');
        // Reset bus delay status to false
        await updateBusDelayStatus(busNumber, false);
        console.log(`Journey ended and history cleared for bus ${busNumber}`);
      }
    } catch (error) {
      console.error('Error ending journey:', error);
    }

    setShowLastStopModal(false);
  };

  // Handle "Start New Journey" button in last stop modal
  const handleLastStopNewJourney = async () => {
    try {
      if (busNumber) {
        // First stop tracking and clear history
        setIsStarted(false);
        setWaves([]);
        // await cancelOngoingRideNotification();
        await stopTracking(true);

        // Clear the bus history
        await clearBusHistory(busNumber);

        // Then start tracking again
        setIsStarted(true);
        startTracking();

        // Update driver and bus status to Active
        const phoneNumber = await AsyncStorage.getItem('phoneNumber');
        if (phoneNumber) {
          await updateDriverStatus(phoneNumber, 'Active');
          await updateBusStatus(busNumber, 'Active');
        }
      }
    } catch (error) {
      console.error('Error starting new journey:', error);
    }

    setShowLastStopModal(false);
  };
  // Update the handler function for the dropdown menu
  const handleUpdatePress = async () => {
    try {
      console.log('Release notes button pressed');

      // Get current version
      const currentVersion = getCurrentVersion ? getCurrentVersion() : '1.0.0';

      // Import and use the release notes from the constants file
      const { CURRENT_RELEASE_NOTES } = require('../constants/ReleaseNotes');

      // Format the notes as a bulleted list
      const formattedNotes = CURRENT_RELEASE_NOTES.notes.map(note => `• ${note}`).join('\n');

      // Emit event to show the release notes modal
      EventRegister.emit('showReleaseNotes', {
        version: CURRENT_RELEASE_NOTES.version || currentVersion,
        date: CURRENT_RELEASE_NOTES.date,
        notes: formattedNotes
      });

    } catch (error) {
      console.error('Error showing release notes:', error);
      Alert.alert(t('tracking.alerts.errorTitle'), t('tracking.alerts.failedReleaseNotes'));
    }
  };

  const handleFuelDataPress = () => {
    Alert.alert(t('tracking.comingSoon.title'), t('tracking.comingSoon.fuelData'));
  };

  const handleSettingsPress = () => {
    Alert.alert(t('tracking.comingSoon.title'), t('tracking.comingSoon.settings'));
  };

  const handleProfilePress = () => {
    Alert.alert(t('tracking.comingSoon.title'), t('tracking.comingSoon.profile'));
  };
  // Modify this useEffect to handle app state changes correctly
  useEffect(() => {
    // Subscribe to AppState changes
    const subscription = AppState.addEventListener('change', nextAppState => {
      console.log(`App state changed from ${appState.current} to ${nextAppState}`);

      // Only set inactive when app is terminated, not when it goes to background
      if (appState.current === 'active' && nextAppState === 'inactive') {
        console.log('App is being terminated');

        // Set bus and driver to inactive only when app is terminated
        const setInactiveOnClose = async () => {
          try {
            if (busNumber) {
              const phoneNumber = await AsyncStorage.getItem('phoneNumber');
              if (phoneNumber) {
                console.log(`Setting bus ${busNumber} and driver ${phoneNumber} to inactive on app termination`);
                await updateDriverStatus(phoneNumber, 'Inactive');
                await updateBusStatus(busNumber, 'Inactive');
                // Unassign the driver from the bus
                await updateDriverAssignedBus(phoneNumber, "Unassigned");
                console.log(`Unassigned bus from driver ${phoneNumber} on app termination`);
                // Remove busNumber and phoneNumber from AsyncStorage to force redirect to Number screen
                await AsyncStorage.removeItem('busNumber');
                await AsyncStorage.removeItem('phoneNumber');
                console.log('Removed busNumber and phoneNumber from AsyncStorage for redirect on next launch');
              }
            }
          } catch (error) {
            console.error('Error setting inactive status on app termination:', error);
          }
        };

        setInactiveOnClose();
      }
      // When app comes back to foreground from background, ensure status is Active if tracking
      else if (appState.current === 'background' && nextAppState === 'active' && isStarted) {
        console.log('App returning to foreground, ensuring active status');

        const ensureActiveStatus = async () => {
          try {
            if (busNumber) {
              const phoneNumber = await AsyncStorage.getItem('phoneNumber');
              if (phoneNumber) {
                console.log(`Ensuring bus ${busNumber} and driver ${phoneNumber} are Active on return to foreground`);
                await updateDriverStatus(phoneNumber, 'Active');
                await updateBusStatus(busNumber, 'Active');
              }
            }
          } catch (error) {
            console.error('Error ensuring active status on return to foreground:', error);
          }
        };

        ensureActiveStatus();
      }

      // Update the appState ref
      appState.current = nextAppState;
    });

    // Clean up on component unmount
    return () => {
      subscription.remove();
    };
  }, [busNumber, isStarted]);
  // Clean up notification when component unmounts
  useEffect(() => {
    return () => {
      // cancelOngoingRideNotification();

      // Also set inactive when component unmounts
      const cleanupOnUnmount = async () => {
        try {
          if (busNumber) {
            const phoneNumber = await AsyncStorage.getItem('phoneNumber');
            if (phoneNumber) {
              console.log(`Setting bus ${busNumber} and driver ${phoneNumber} to inactive on unmount`);
              await updateDriverStatus(phoneNumber, 'Inactive');
              await updateBusStatus(busNumber, 'Inactive');
              // Unassign the driver from the bus
              await updateDriverAssignedBus(phoneNumber, "Unassigned");
              console.log(`Unassigned bus from driver ${phoneNumber} on component unmount`)
            }
          }
        } catch (error) {
          console.error('Error setting inactive status on unmount:', error);
        }
      };

      cleanupOnUnmount();
    };
  }, [busNumber]);
  return (
    <View style={styles.container}>
      {isDropdownOpen && (
        <TouchableOpacity
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            width: '100%',
            height: '100%',
            zIndex: 999,
          }}
          activeOpacity={0}
          onPress={() => setIsDropdownOpen(false)}
        />
      )}
      <ImageBackground
        source={mode === 'dark' ? MapsImageDark : MapsImage}
        style={styles.backgroundImage}
        resizeMode="cover"
        imageStyle={styles.backgroundImageStyle}
      >
        {/* Loading Overlay */}
        {isBusDataLoading && (
          <View style={styles.loadingOverlay}>
            <View style={styles.loadingContainer}>
              <Animated.View style={[
                styles.loadingIndicator,
                {
                  opacity: loadingAnimation,
                  transform: [{
                    scale: loadingAnimation.interpolate({
                      inputRange: [0, 1],
                      outputRange: [0.8, 1.2]
                    })
                  }]
                }
              ]}>
                <FontAwesome6
                  name="bus"
                  size={40}
                  color={theme.mode === 'light' ? '#FFFFFF' : theme.busIconColor}
                />
              </Animated.View>
              <Text style={styles.loadingText}>{t('tracking.loading.busData')}</Text>
            </View>
          </View>
        )}
        {/* Modified DropdownMenu with isOpen state and onToggle callback */}
        <DropdownMenu
          isOpen={isDropdownOpen}
          onToggle={handleDropdownToggle}
          onUpdatePress={handleUpdatePress}
          onFuelDataPress={handleFuelDataPress}
          onSettingsPress={handleSettingsPress}
          onProfilePress={handleProfilePress}
        />

        {/* Header Card */}
        <View style={styles.headerCard}>
          <View style={styles.headerTopRow}>
            <FontAwesome6
              name="bus"
              size={40}
              color={theme.busIconColor}
              style={styles.trainIcon}
            />
            <Text style={styles.busNumber}>{busNumber}</Text>
          </View>
          <View style={styles.separator} />
          <View style={styles.routeRow}>
            <View style={styles.routeCircleContainer}>
              <View style={styles.routeCircle}>
                <Text style={styles.routeLabel}>Mdy</Text>
              </View>
              <Text style={styles.routeSubtitle}>{t('tracking.route.mayiladuthurai')}</Text>
            </View>
            <View style={[styles.dottedLine]} />
            <View style={styles.routeCircleContainer}>
              <View style={styles.routeCircle}>
                <Text style={styles.routeLabel}>KKL</Text>
              </View>
              <Text style={styles.routeSubtitle}>{t('tracking.route.karaikal')}</Text>
            </View>
          </View>
        </View>

        {/* Combined Header Buttons */}
        <View style={styles.headerButtonsContainer}>
          <LogoutButton />
          <ColorModeToggle />
        </View>

        {/* Main Content */}
        <View style={styles.mainContent}>
          {waves.map(key => (
            <RadarWave key={key} onComplete={() => removeWave(key)} />
          ))}

          <View style={{ position: 'relative', alignItems: 'center' }}>
            <Animated.View
              style={[{ transform: [{ scale: pressAnim }] }, isPressing ? pressedShadow : unpressedShadow]}
            >
              <TouchableOpacity
                activeOpacity={0.8}
                onPressIn={onPressIn}
                onPressOut={onPressOut}
                // Inside the TouchableOpacity onPress handler:

                onPress={async () => {
                  if (!isStarted) {
                    console.log("Bus started button pressed");

                    // Set state to started immediately
                    setIsStarted(true);

                    // Update driver and bus status to Active first
                    try {
                      // Get phone number from route params or AsyncStorage
                      let phoneNumber = route.params?.phoneNumber;
                      console.log('Phone number from route params:', phoneNumber);

                      if (!phoneNumber) {
                        phoneNumber = await AsyncStorage.getItem('phoneNumber');
                        console.log('Phone number from AsyncStorage:', phoneNumber);
                      }

                      // Verify bus number
                      console.log('Bus number from route params:', busNumber);
                      const storedBusNumber = await AsyncStorage.getItem('busNumber');
                      console.log('Bus number from AsyncStorage:', storedBusNumber);

                      // Use the most reliable source for bus number
                      const finalBusNumber = busNumber || storedBusNumber;

                      if (phoneNumber && finalBusNumber) {
                        console.log(`Setting bus ${finalBusNumber} and driver ${phoneNumber} to Active on button press`);

                        // Store both values in AsyncStorage to ensure consistency
                        await AsyncStorage.setItem('phoneNumber', phoneNumber);
                        await AsyncStorage.setItem('busNumber', finalBusNumber);

                        // Update status immediately
                        await updateDriverStatus(phoneNumber, 'Active');
                        await updateBusStatus(finalBusNumber, 'Active');
                      } else {
                        console.error('Missing phone number or bus number for status update');
                        if (!phoneNumber) console.error('Phone number is missing');
                        if (!finalBusNumber) console.error('Bus number is missing');
                      }
                    } catch (error) {
                      console.error('Error updating status on button press:', error);
                    }

                    // Then start tracking
                    startTracking();
                  }
                }}
                disabled={isStarted}
              >
                <LinearGradient
                  colors={theme.startButtonColor}
                  start={{ x: 0.5, y: 0 }}
                  end={{ x: 0.5, y: 1 }}
                  style={styles.startButton}
                >
                  <Text style={getButtonTextStyle()}>
                    {isStarted ? t('tracking.buttonState.running') : t('tracking.buttonState.start')}
                  </Text>
                </LinearGradient>
              </TouchableOpacity>
            </Animated.View>

            {isStarted && (
              <TouchableOpacity
                onPress={handleStopPress}
                style={styles.stopIconAbsolute}
              >
                <FontAwesome6 name="xmark-circle" size={50} color={theme.XIconColor} />
              </TouchableOpacity>
            )}
          </View>
          {/* Change test button to open StopRideModal instead of LastStopModal */}
          {/* <TouchableOpacity
            style={styles.testButton}
            onPress={() => setShowLastStopModal(true)}
          >
            <Text style={styles.testButtonText}>Test Last Stop Modal</Text>
          </TouchableOpacity> */}
        </View>
      </ImageBackground>
      {/* Add the ResumeRideModal component */}
      <ResumeRideModal
        visible={showResumeModal}
        onResume={handleResumeRide}
        onNewRide={confirmStopRide}
        hasCurrentCoordinates={hasCurrentCoordinates}
        hasHistory={hasHistory}
      />
      {/* Add the StopRideModal component with busNumber prop */}
      <StopRideModal
        visible={showStopModal}
        onCancel={() => setShowStopModal(false)}
        onConfirm={() => {
          confirmStopRide();
          setShowStopModal(false);
        }}
        busNumber={busNumber}
      />
      {/* Last Stop Modal - removed onLater prop */}
      <LastStopModal
        visible={showLastStopModal}
        onEndJourney={handleLastStopEndJourney}
        onStartNewJourney={handleLastStopNewJourney}
        busNumber={busNumber}
      />
      <ReleaseNotesModal
        visible={showReleaseNotesModal}
        onClose={() => setShowReleaseNotesModal(false)}
        version={releaseNotesVersion}
        date={releaseNotesDate}
        notes={releaseNotesContent}
      />
    </View>
  );
}

// RadarWave Component for animated waves
function RadarWave({ onComplete }) {
  const { theme } = useContext(ThemeContext);
  const scale = useRef(new Animated.Value(1)).current;
  const opacity = useRef(new Animated.Value(0.5)).current;

  useEffect(() => {
    Animated.parallel([
      Animated.timing(scale, {
        toValue: 3,
        duration: 2000,
        easing: Easing.linear,
        useNativeDriver: true,
      }),
      Animated.timing(opacity, {
        toValue: 0,
        duration: 1000,
        easing: Easing.linear,
        useNativeDriver: true,
      }),
    ]).start(({ finished }) => {
      if (finished && onComplete) {
        onComplete();
      }
    });
  }, [scale, opacity, onComplete]);

  return (
    <Animated.View
      pointerEvents="none"
      style={{
        position: 'absolute',
        width: 200,
        height: 200,
        borderRadius: 100,
        borderWidth: 100,
        borderColor: theme.startButtonColor[0],
        transform: [{ scale }],
        opacity,
      }}
    />
  );
}
