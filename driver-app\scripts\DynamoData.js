import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import {
  DynamoDBDocumentClient,
  GetCommand,
  PutCommand,
  UpdateCommand,
  QueryCommand,
  ScanCommand,
  DeleteCommand,
  BatchGetCommand
} from '@aws-sdk/lib-dynamodb';

import 'react-native-get-random-values';

// Configure AWS SDK with credentials & region
const client = new DynamoDBClient({
  region: 'ap-south-2',
  credentials: {
    accessKeyId: '********************',
    secretAccessKey: 'TSim9/96TQGaPQ7dlnyfvNZ9Y5alXO2Ycr+SWY62',
  },
});

// Initialize DynamoDB DocumentClient
const dynamoDB = DynamoDBDocumentClient.from(client);

// Rest of your code will need to be updated to use the new command pattern
/**
 * Get item from DynamoDB by id.
 * @param {string} tableName - Name of the DynamoDB table.
 * @param {number|string} id - The id to look up.
 * @returns {Promise<object|null>} - The retrieved item or null if not found.
 */
async function getBusMasterData(tableName, id) {
  const params = {
    TableName: tableName,
    Key: { id: Number(id) }
  };

  try {
    const data = await dynamoDB.send(new GetCommand(params));
    return data.Item;
  } catch (error) {
    console.error("Error fetching data:", error);
    return null;
  }
}

/**
 * Update bus coordinates in DynamoDB
 * @param {string} busNumber - The bus number (id)
 * @param {object} coordinates - The coordinates object {latitude, longitude}
 * @param {Array} cachedBusStops - Optional cached bus stops to use instead of fetching
 * @returns {Promise<object>} - Object with status and lastStop flag
 */
async function updateBusLocation(busNumber, coordinates, cachedBusStops = null) {
  // console.log("busnumber:", busNumber);
  // console.log('coordinates:', coordinates);

  const params = {
    TableName: "Buses-DB",
    Key: {
      // Use 'busNumber' here, because that's the partition key in Buses-DB
      busNumber: String(busNumber),
    },
    UpdateExpression: "SET currentCoordinates = :coords",
    ExpressionAttributeValues: {
      ":coords": `${coordinates.latitude},${coordinates.longitude}`
    }
  };

  try {
    await dynamoDB.send(new UpdateCommand(params));
    console.log("while update bus location checking cacheddata:",cachedBusStops?.length || 0);
    
    // Check if the bus is at a stop and update history if needed
    // Pass the cached bus stops to avoid fetching them again
    return await checkAndUpdateBusStopHistory(
      busNumber,
      coordinates.latitude,
      coordinates.longitude,
      cachedBusStops
    );
  } catch (error) {
    console.error("Error updating bus location:", error);
    return { updated: false, isLastStop: false };
  }
}

/**
 * Get all bus stop coordinates from the busStops field in Buses-DB for a specific bus
 * @param {string} busNumber - The bus number to get stops for
 * @returns {Promise<Array<object>>} Array of bus stop objects
 */
async function getBusStops(busNumber) {
  const params = {
    TableName: "Buses-DB",
    Key: {
      busNumber: String(busNumber)
    }
  };
  try {
    const data = await dynamoDB.send(new GetCommand(params));

    if (!data.Item || !data.Item.busStops) {
      console.error(`No bus stops found for bus ${busNumber}`);
      return [];
    }

    // console.log(`Retrieved bus stops for bus ${busNumber}:`, JSON.stringify(data.Item.busStops));

    // Get the raw bus stops array - this should be a simple array of strings
    const busStops = data.Item.busStops;

    // Ensure we're working with an array
    if (!Array.isArray(busStops)) {
      console.error(`Bus stops data is not an array for bus ${busNumber}`);
      return [];
    }

    // console.log("Bus stops array:", busStops);

    // Now fetch the coordinates for each stop from BusStops-DB
    const stopsWithCoordinates = await Promise.all(
      busStops.map(async (stopName) => {
        // Handle both string values and objects with S property
        const actualStopName = typeof stopName === 'string' ? stopName :
          (stopName.S ? stopName.S : String(stopName));

        // console.log(`Processing stop: ${actualStopName}`);
        const stopData = await fetchBusStopDetails(actualStopName);

        return {
          stopName: actualStopName,
          coordinates: stopData ? stopData.coordinates : "0,0" // Use actual coordinates or fallback
        };
      })
    );

    // console.log("Stops with coordinates:", stopsWithCoordinates);
    return stopsWithCoordinates;
  } catch (error) {
    console.error(`Error fetching bus stops for bus ${busNumber}:`, error);
    console.error("Error details:", error.stack);
    return [];
  }
}

/**
 * Get coordinates for a specific bus stop from BusStops-DB
 * @param {string} stopName - The name of the bus stop
 * @returns {Promise<object|null>} - The bus stop data or null if not found
 */
async function fetchBusStopDetails(stopName) {
  const params = {
    TableName: "BusStops-DB",
    Key: {
      stopName: String(stopName)
    }
  };

  try {
    const data = await dynamoDB.send(new GetCommand(params));
    return data.Item;
  } catch (error) {
    console.error(`Error fetching coordinates for stop ${stopName}:`, error);
    return null;
  }
}
/**
 * Check if bus is near a stop and update history if needed
 * @param {string} busNumber - The bus number
 * @param {number} currentLat - Current latitude
 * @param {number} currentLng - Current longitude
 * @param {Array} cachedBusStops - Optional cached bus stops
 * @returns {Promise<object>} - Object with updated status and isLastStop flag
 */
async function checkAndUpdateBusStopHistory(busNumber, currentLat, currentLng, cachedBusStops = null) {
  try {
    console.log(`Checking bus stop history for bus ${busNumber} at coordinates: ${currentLat}, ${currentLng}`);
    
    // STEP 1: Get current bus history
    const getHistoryParams = {
      TableName: "Buses-DB",
      Key: { busNumber: String(busNumber) },
      ProjectionExpression: "history"
    };

    const historyData = await dynamoDB.send(new GetCommand(getHistoryParams));
    let currentHistory = [];

    // Parse history data from DynamoDB
    if (historyData.Item && historyData.Item.history) {
      if (Array.isArray(historyData.Item.history)) {
        currentHistory = historyData.Item.history;
      } else if (historyData.Item.history.L) {
        currentHistory = historyData.Item.history.L.map(item => 
          item.S || String(item)
        );
      }
    }
    
    console.log(`Current history entries: ${currentHistory.length}`);

    // STEP 2: Get and validate bus stops data
    console.log("after getting history we check cachedBusStops:", cachedBusStops?.length || 0);
    if (!cachedBusStops || !Array.isArray(cachedBusStops) || cachedBusStops.length === 0) {
      try {
        console.log(`No cached bus stops, fetching from database for bus ${busNumber}`);
        // Try to get bus stops from the newer function first
        let fetchedBusStops = null;
        
        try {
          const busData = await getBusDataWithStops(busNumber);
          if (busData && busData.busStops && busData.busStops.length > 0) {
            fetchedBusStops = busData.busStops;
            console.log(`Retrieved ${fetchedBusStops.length} bus stops from getBusDataWithStops`);
          }
        } catch (innerError) {
          console.log(`Error in getBusDataWithStops: ${innerError.message}, falling back to getBusStops`);
        }
        
        // Fall back to older method if needed
        if (!fetchedBusStops) {
          fetchedBusStops = await getBusStops(busNumber);
          console.log(`Retrieved ${fetchedBusStops ? fetchedBusStops.length : 0} bus stops from getBusStops`);
        }
        
        cachedBusStops = fetchedBusStops;
      } catch (error) {
        console.error(`Error fetching bus stops: ${error.message}`);
        return { updated: false, isLastStop: false };
      }
    }

    // Validate we have bus stops data
    if (!cachedBusStops || cachedBusStops.length === 0) {
      console.error(`No bus stops found for bus ${busNumber}`);
      return { updated: false, isLastStop: false };
    }

    // Validate coordinates
    const parsedLat = parseFloat(currentLat);
    const parsedLng = parseFloat(currentLng);
    
    if (isNaN(parsedLat) || isNaN(parsedLng)) {
      console.error(`Invalid coordinates for bus ${busNumber}: ${currentLat}, ${currentLng}`);
      return { updated: false, isLastStop: false };
    }

    // STEP 3: Find the nearest bus stop within 150 meters (0.15 km)
    let nearestStop = null;
    let nearestDistance = Infinity;
    let nearestStopIndex = -1;

    for (let i = 0; i < cachedBusStops.length; i++) {
      const stop = cachedBusStops[i];
      
      // Skip stops without coordinates
      if (!stop.coordinates) {
        console.log(`Stop ${stop.stopName || 'unknown'} has no coordinates, skipping`);
        continue;
      }

      // Parse stop coordinates
      const [stopLat, stopLng] = stop.coordinates.split(',').map(coord => parseFloat(coord));
      
      // Skip stops with invalid coordinates
      if (isNaN(stopLat) || isNaN(stopLng)) {
        console.log(`Stop ${stop.stopName || 'unknown'} has invalid coordinates: ${stop.coordinates}`);
        continue;
      }
      
      // Calculate distance to stop
      const distance = calculateDistance(parsedLat, parsedLng, stopLat, stopLng);
      
      if (distance < nearestDistance) {
        nearestDistance = distance;
        nearestStop = stop;
        nearestStopIndex = i;
      }
    }

    // Check if we're near a stop (within 150 meters = 0.15 km)
    const isNearStop = nearestStop && nearestDistance <= 0.15;
    
    if (!isNearStop) {
      console.log(`Bus ${busNumber} is not near any stop (nearest: ${nearestStop?.stopName || 'none'} at ${nearestDistance.toFixed(3)} km)`);
      return { updated: false, isLastStop: false };
    }

    console.log(`Bus ${busNumber} is near stop: ${nearestStop.stopName} (${nearestDistance.toFixed(3)} km)`);

    // STEP 4: Check if this is a duplicate of the last recorded stop
    if (currentHistory.length > 0) {
      const lastEntry = currentHistory[currentHistory.length - 1];
      const lastEntryParts = lastEntry.split('#');
      
      if (lastEntryParts.length >= 3) {
        const lastStopName = lastEntryParts[0].toLowerCase().trim();
        const currentStopName = nearestStop.stopName.toLowerCase().trim();
        
        // Get the index from the last entry if available
        let lastStopIndex = -1;
        const lastIdxPart = lastEntryParts.find(part => part.startsWith("IDX"));
        if (lastIdxPart) {
          lastStopIndex = parseInt(lastIdxPart.substring(3));
        }
        
        // Check if both the name AND index match (for duplicate stops in the route)
        if (lastStopName === currentStopName && lastStopIndex === nearestStopIndex) {
          console.log(`Already recorded stop ${nearestStop.stopName} at index ${nearestStopIndex}. Skipping duplicate entry.`);
          
          // Check if this is the last stop for notification purposes
          const isLastStop = nearestStopIndex === cachedBusStops.length - 1;
          return { updated: false, isLastStop };
        }
      }
    }

    // Get current date and time
    const now = new Date();
    const today = now.toISOString().slice(0, 10); // YYYY-MM-DD
    const currentTime = now.toTimeString().slice(0, 8); // HH:MM:SS

    // STEP 5: Check for missed stops
    // Find the most recent legitimate stop index (not redirected)
    let lastRecordedStopIndex = -1;
    
    for (let i = currentHistory.length - 1; i >= 0; i--) {
      const entry = currentHistory[i];
      const parts = entry.split('#');
      
      // Skip redirected entries
      if (parts.includes("Redirected or Data not found")) continue;
      
      // Get the index from IDX part if available
      const idxPart = parts.find(part => part.startsWith("IDX"));
      if (idxPart) {
        lastRecordedStopIndex = parseInt(idxPart.substring(3));
        break;
      }
    }

    console.log(`Last recorded stop index: ${lastRecordedStopIndex}, Current stop index: ${nearestStopIndex}`);

    // Handle missed stops
    const missedStops = [];
    
    // If this is not the first stop and there are missed stops in between
    if (lastRecordedStopIndex >= 0 && nearestStopIndex > lastRecordedStopIndex + 1) {
      for (let i = lastRecordedStopIndex + 1; i < nearestStopIndex; i++) {
        const missedStop = cachedBusStops[i];
        missedStops.push({
          stopName: missedStop.stopName,
          index: i
        });
      }
    } 
    // If this is the first recorded stop but not the first in the route
    else if (lastRecordedStopIndex === -1 && nearestStopIndex > 0) {
      for (let i = 0; i < nearestStopIndex; i++) {
        const missedStop = cachedBusStops[i];
        missedStops.push({
          stopName: missedStop.stopName,
          index: i
        });
      }
    }

    // STEP 6: Record redirected stops
    if (missedStops.length > 0) {
      console.log(`Recording ${missedStops.length} missed stops as redirected`);
      
      const redirectEntries = [];
      for (const missedStop of missedStops) {
        // Format: "stopName#Redirected or Data not found#time#date#IDX{index}"
        const redirectEntry = `${missedStop.stopName}#Redirected or Data not found#${currentTime}#${today}#IDX${missedStop.index}`;
        redirectEntries.push(redirectEntry);
      }
      
      try {
        // Use batch update to add all missed stops in a single AWS request
        const batchResult = await batchUpdateBusHistory(busNumber, redirectEntries);
        if (batchResult) {
          console.log(`Successfully recorded ${redirectEntries.length} redirected stops for bus ${busNumber}`);
        } else {
          console.error(`Failed to batch record redirected stops for bus ${busNumber}`);
        }
      } catch (redirectError) {
        console.error(`Error batch recording redirected stops for bus ${busNumber}:`, redirectError);
      }
    }

    // STEP 7: Record the current stop
    // Format: "stopName#Reached#time#date#IDX{index}"
    const historyEntry = `${nearestStop.stopName}#Reached#${currentTime}#${today}#IDX${nearestStopIndex}`;
    
    try {
      const updated = await updateBusHistory(busNumber, historyEntry);
      
      if (!updated) {
        console.log(`Failed to record stop ${nearestStop.stopName} for bus ${busNumber}`);
        return { updated: false, isLastStop: false };
      }
      
      console.log(`Successfully recorded stop ${nearestStop.stopName} for bus ${busNumber} at index ${nearestStopIndex}`);
      
      // STEP 8: Check if this is the last stop in the route
      const isLastStop = nearestStopIndex === cachedBusStops.length - 1;
      
      if (isLastStop) {
        console.log(`Bus ${busNumber} has reached the last stop: ${nearestStop.stopName}`);
      }
      
      return { updated: true, isLastStop };
    } catch (updateError) {
      console.error(`Error updating bus history for stop ${nearestStop.stopName}:`, updateError);
      return { updated: false, isLastStop: false };
    }
  } catch (error) {
    console.error(`Error in checkAndUpdateBusStopHistory for bus ${busNumber}:`, error);
    console.error(`Error stack: ${error.stack}`);
    return { updated: false, isLastStop: false };
  }
}

// Also update the updateBusHistory function to handle the new format
async function updateBusHistory(busNumber, historyEntry, skipDuplicateCheck = false) {
  try {
    // Extract stop name from the history entry
    const parts = historyEntry.split('#');
    if (parts.length < 4) {
      console.error(`Invalid history entry format: ${historyEntry}`);
      return false;
    }

    const stopName = parts[0];
    const status = parts[1];
    const timeString = parts[2];
    const dateString = parts[3];
    const indexPart = parts[4] || '';

    console.log(`Updating history for bus ${busNumber}: ${stopName} (${status}) at ${timeString} on ${dateString} ${indexPart}`);

    // Retrieve current history from DynamoDB
    const getParams = {
      TableName: "Buses-DB",
      Key: { busNumber: String(busNumber) }
    };

    const data = await dynamoDB.send(new GetCommand(getParams));
    let currentHistory = [];

    // Handle DynamoDB's L (List) type format
    if (data.Item && data.Item.history) {
      if (data.Item.history.L) {
        // Extract string values from DynamoDB List format
        currentHistory = data.Item.history.L.map(item =>
          item.S || String(item)
        );
      } else if (Array.isArray(data.Item.history)) {
        currentHistory = data.Item.history;
      }
    }

    // Check if this is a duplicate of the last entry (within 2 minutes)
    if (!skipDuplicateCheck && currentHistory.length > 0) {
      const lastEntry = currentHistory[currentHistory.length - 1];
      const lastParts = lastEntry.split('#');
      
      if (lastParts.length >= 3) {
        const lastStopName = lastParts[0].toLowerCase().trim();
        const currentStopName = stopName.toLowerCase().trim();
        const lastTime = lastParts[2];
        
        // Get the index from both entries if available
        let lastStopIndex = -1;
        let currentStopIndex = -1;
        
        const lastIdxPart = lastParts.find(part => part.startsWith("IDX"));
        if (lastIdxPart) {
          lastStopIndex = parseInt(lastIdxPart.substring(3));
        }
        
        const currentIdxPart = parts.find(part => part.startsWith("IDX"));
        if (currentIdxPart) {
          currentStopIndex = parseInt(currentIdxPart.substring(3));
        }
        
        // If same stop name AND same index, check time difference
        if (lastStopName === currentStopName && lastStopIndex === currentStopIndex) {
          // Convert times to seconds for comparison
          const timeDiff = Math.abs(timeToSeconds(lastTime) - timeToSeconds(timeString));
          
          // If within 2 minutes (120 seconds), skip
          if (timeDiff < 120) {
            console.log(`Duplicate entry detected for ${currentStopName} at index ${currentStopIndex} within ${Math.floor(timeDiff / 60)} minutes and ${timeDiff % 60} seconds. Skipping.`);
            return false;
          }
        }
      }
    }

    // Append the new history entry
    currentHistory.push(historyEntry);

    // Update the DynamoDB record with the new history array
    const updateParams = {
      TableName: "Buses-DB",
      Key: { busNumber: String(busNumber) },
      UpdateExpression: "SET history = :history",
      ExpressionAttributeValues: {
        ":history": currentHistory
      }
    };

    await dynamoDB.send(new UpdateCommand(updateParams));
    console.log(`Successfully updated history for bus ${busNumber} with entry: ${historyEntry}`);
    return true;
  } catch (error) {
    console.error(`Error updating bus history for bus ${busNumber}:`, error);
    console.error(`Error stack: ${error.stack}`);
    return false;
  }
}

/**
 * Batch update the bus history with multiple entries in a single AWS request
 * @param {string} busNumber - The bus number
 * @param {Array<string>} historyEntries - Array of history entries to add
 * @returns {Promise<boolean>} - Success status
 */
async function batchUpdateBusHistory(busNumber, historyEntries) {
  try {
    if (!historyEntries || historyEntries.length === 0) {
      console.log(`No history entries to update for bus ${busNumber}`);
      return true;
    }

    console.log(`Batch updating history for bus ${busNumber} with ${historyEntries.length} entries`);

    // Retrieve current history from DynamoDB
    const getParams = {
      TableName: "Buses-DB",
      Key: { busNumber: String(busNumber) }
    };

    const data = await dynamoDB.send(new GetCommand(getParams));
    let currentHistory = [];

    // Handle DynamoDB's L (List) type format
    if (data.Item && data.Item.history) {
      if (data.Item.history.L) {
        // Extract string values from DynamoDB List format
        currentHistory = data.Item.history.L.map(item =>
          item.S || String(item)
        );
      } else if (Array.isArray(data.Item.history)) {
        currentHistory = data.Item.history;
      }
    }

    // Create a map of existing entries by index to check for duplicates
    const existingEntriesByIndex = {};
    currentHistory.forEach(entry => {
      const parts = entry.split('#');
      const idxPart = parts.find(part => part.startsWith("IDX"));
      if (idxPart) {
        const index = parseInt(idxPart.substring(3));
        existingEntriesByIndex[index] = entry;
      }
    });

    // Filter out any duplicates by index before adding
    const newEntries = historyEntries.filter(entry => {
      const parts = entry.split('#');
      const idxPart = parts.find(part => part.startsWith("IDX"));
      
      // If we can't determine the index, include it by default
      if (!idxPart) return true;
      
      const index = parseInt(idxPart.substring(3));
      // Include if the index doesn't exist in our current history
      return !existingEntriesByIndex[index];
    });

    if (newEntries.length === 0) {
      console.log(`All entries already exist in history for bus ${busNumber}`);
      return true;
    }

    // Append all new entries at once
    const updatedHistory = [...currentHistory, ...newEntries];

    // Update the DynamoDB record with all entries in a single operation
    const updateParams = {
      TableName: "Buses-DB",
      Key: { busNumber: String(busNumber) },
      UpdateExpression: "SET history = :history",
      ExpressionAttributeValues: {
        ":history": updatedHistory
      }
    };

    await dynamoDB.send(new UpdateCommand(updateParams));
    console.log(`Successfully batch updated history for bus ${busNumber} with ${newEntries.length} new entries`);
    return true;
  } catch (error) {
    console.error(`Error batch updating bus history for bus ${busNumber}:`, error);
    console.error(`Error stack: ${error.stack}`);
    return false;
  }
}

// Helper function to convert HH:MM:SS to seconds
function timeToSeconds(timeStr) {
  const [hours, minutes, seconds] = timeStr.split(':').map(Number);
  return hours * 3600 + minutes * 60 + seconds;
}

/**
 * Update bus status in DynamoDB
 * @param {string} busNumber - The bus number
 * @param {string} status - The status ('Active' or 'Inactive')
 * @returns {Promise<object>} - The result of the update operation
 */
async function updateBusStatus(busNumber, status) {
  // console.log('busnumber:', busNumber);
  // console.log('status:', status);

  const params = {
    TableName: "Buses-DB",
    Key: {
      busNumber: String(busNumber)
    },
    UpdateExpression: "SET #status = :status",
    ExpressionAttributeNames: {
      "#status": "status"
    },
    ExpressionAttributeValues: {
      ":status": status
    },
    ReturnValues: "ALL_NEW"
  };

  try {
    const result = await dynamoDB.send(new UpdateCommand(params));
    console.log(`Updated status for bus ${busNumber} to ${status}`);
    return {
      success: true,
      message: `Updated status for bus ${busNumber} to ${status}`,
      data: result.Attributes
    };
    // console.log(`Updated status for bus ${busNumber} to ${status}`);
  } catch (error) {
    console.error("Error updating bus status:", error);
    return {
      success: false,
      message: `Failed to update status for bus ${busNumber}`,
      error: error.message
    };
  }
}

/**
 * Clear bus coordinates in DynamoDB
 * @param {string} busNumber - The bus number
 */
async function clearBusCoordinates(busNumber) {
  const params = {
    TableName: "Buses-DB",
    Key: {
      busNumber: String(busNumber)
    },
    UpdateExpression: "REMOVE currentCoordinates"
  };

  try {
    await dynamoDB.send(new UpdateCommand(params));
    console.log(`Cleared coordinates for bus ${busNumber}`);
  } catch (error) {
    console.error("Error clearing bus coordinates:", error);
  }
}

/**
 * Update driver's assigned bus in DynamoDB
 * @param {string} phoneNumber - The driver's phone number
 * @param {string} busNumber - The assigned bus number
 */
async function updateDriverAssignedBus(phoneNumber, busNumber) {
  const params = {
    TableName: "Drivers-DB",
    Key: {
      id: String(phoneNumber)
    },
    UpdateExpression: "SET currentBus = :busNumber",
    ExpressionAttributeValues: {
      ":busNumber": String(busNumber)
    }
  };

  try {
    await dynamoDB.send(new UpdateCommand(params));
    // console.log(`Updated assigned bus for driver ${phoneNumber} to ${busNumber}`);
  } catch (error) {
    console.error("Error updating driver's assigned bus:", error);
  }
}

/**
 * Update driver status in DynamoDB
 * @param {string} phoneNumber - The driver's phone number
 * @param {string} status - The status ('Active' or 'Inactive')
 * @returns {Promise<object>} - The result of the update operation
 */
async function updateDriverStatus(phoneNumber, status) {
  const params = {
    TableName: "Drivers-DB",
    Key: {
      id: String(phoneNumber)
    },
    UpdateExpression: "SET #status = :status",
    ExpressionAttributeNames: {
      "#status": "status"
    },
    ExpressionAttributeValues: {
      ":status": status
    },
    ReturnValues: "ALL_NEW" 
  };

  try {
    const result = await dynamoDB.send(new UpdateCommand(params));
    console.log(`Updated status for driver ${phoneNumber} to ${status}`);
    return {
      success: true,
      message: `Updated status for driver ${phoneNumber} to ${status}`,
      data: result.Attributes
    };
    // console.log(`Updated status for driver ${phoneNumber} to ${status}`);
  } catch (error) {
    console.error("Error updating driver status:", error);
    return {
      success: false,
      message: `Failed to update status for driver ${phoneNumber}`,
      error: error.message
    };
  }
}
/**
 * Clear bus history in DynamoDB
 * @param {string} busNumber - The bus number
 */
async function clearBusHistory(busNumber) {
  const params = {
    TableName: "Buses-DB",
    Key: {
      busNumber: String(busNumber)
    },
    UpdateExpression: "SET history = :emptyList",
    ExpressionAttributeValues: {
      ":emptyList": []
    }
  };

  try {
    await dynamoDB.send(new UpdateCommand(params));
    console.log(`Cleared history for bus ${busNumber}`);
  } catch (error) {
    console.error("Error clearing bus history:", error);
  }
}
/**
 * Get bus history from DynamoDB
 * @param {string} busNumber - The bus number
 * @returns {Promise<object|null>} - The bus data or null if not found
 */
async function getBusHistory(busNumber) {
  const params = {
    TableName: "Buses-DB",
    Key: {
      busNumber: String(busNumber)
    }
  };

  try {
    const data = await dynamoDB.send(new GetCommand(params));
    return data.Item;
  } catch (error) {
    console.error(`Error fetching history for bus ${busNumber}:`, error);
    return null;
  }
}
/**
* Get available (inactive) buses from the master list
 * @param {Array<string>} busList - List of all bus numbers from master data
 * @returns {Promise<Array<string>>} - List of available bus numbers
 */
async function getAvailableBuses(busList) {
  if (!Array.isArray(busList) || busList.length === 0) {
    return [];
  }

  try {
    // Get status of all buses from the master list
    const busStatusPromises = busList.map(async (busNumber) => {
      const params = {
        TableName: "Buses-DB",
        Key: { busNumber: String(busNumber) }
      };

      try {
        const data = await dynamoDB.send(new GetCommand(params));
        return {
          busNumber,
          status: data.Item?.status || 'Inactive' // Default to Inactive if status not found
        };
      } catch (error) {
        console.error(`Error fetching status for bus ${busNumber}:`, error);
        return { busNumber, status: 'Unknown' };
      }
    });

    const busStatuses = await Promise.all(busStatusPromises);

    // Filter to only include inactive buses
    const inactiveBuses = busStatuses
      .filter(bus => bus.status === 'Inactive')
      .map(bus => bus.busNumber);

    console.log('Available buses (inactive only):', inactiveBuses);
    return inactiveBuses;
  } catch (error) {
    console.error('Error fetching bus statuses:', error);
    return busList; // Return all buses if there's an error
  }
}

/**
 * Calculate distance between two coordinates using Haversine formula
 * @param {number} lat1 - Latitude of point 1
 * @param {number} lng1 - Longitude of point 1
 * @param {number} lat2 - Latitude of point 2
 * @param {number} lng2 - Longitude of point 2
 * @returns {number} - Distance in kilometers
 */
function calculateDistance(lat1, lng1, lat2, lng2) {
  const R = 6371; // Radius of the earth in km
  const dLat = deg2rad(lat2 - lat1);
  const dLng = deg2rad(lng2 - lng1);
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(deg2rad(lat1)) * Math.cos(deg2rad(lat2)) *
    Math.sin(dLng / 2) * Math.sin(dLng / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  const distance = R * c; // Distance in km
  return distance;
}

function deg2rad(deg) {
  return deg * (Math.PI / 180);
}
/**
 * Start a new journey by clearing history
 * @param {string} busNumber - The bus number
 * @returns {Promise<boolean>} - Success status
 */
async function startNewJourney(busNumber) {
  try {
    // Clear the history for this bus
    await clearBusHistory(busNumber);

    // Update the bus status to Active
    await updateBusStatus(busNumber, 'Active');

    console.log(`Started new journey for bus ${busNumber}`);
    return true;
  } catch (error) {
    console.error(`Error starting new journey for bus ${busNumber}:`, error);
    return false;
  }
}
/**
 * End the current journey
 * @param {string} busNumber - The bus number
 * @param {boolean} keepHistory - Whether to keep the history
 * @returns {Promise<boolean>} - Success status
 */
async function endJourney(busNumber, keepHistory = false) {
  try {
    // If we don't want to keep history, clear it
    if (!keepHistory) {
      await clearBusHistory(busNumber);
    }

    // Update the bus status to Inactive
    await updateBusStatus(busNumber, 'Inactive');

    // Clear current coordinates to indicate the bus is not moving
    await clearBusCoordinates(busNumber);

    // Reset tracking variables - fixed the syntax error by using separate operations
    console.log(`Ended journey for bus ${busNumber} and reset all tracking data`);
    return true;
  } catch (error) {
    console.error(`Error ending journey for bus ${busNumber}:`, error);
    return false;
  }
}
/**
 * Get the current coordinates for a bus
 * @param {string} busNumber - The bus number
 * @returns {Promise<Object|null>} - The current coordinates or null
 */
async function getBusCurrentCoordinates(busNumber) {
  try {
    const params = {
      TableName: "Buses-DB",
      Key: {
        busNumber: String(busNumber)
      }
    };

    const data = await dynamoDB.send(new GetCommand(params));

    if (data.Item && data.Item.currentCoordinates) {
      // Parse the coordinates string "lat,lng" into an object
      const coordsStr = data.Item.currentCoordinates;
      // Handle both string values and objects with S property (DynamoDB format)
      const actualCoordsStr = typeof coordsStr === 'string' ? coordsStr :
        (coordsStr.S ? coordsStr.S : String(coordsStr));

      const [latitude, longitude] = actualCoordsStr.split(',').map(coord => parseFloat(coord));

      return { latitude, longitude };
    }

    return null;
  } catch (error) {
    console.error('Error getting bus current coordinates:', error);
    return null;
  }
}

/**
 * Save the current coordinates for a bus
 * @param {string} busNumber - The bus number
 * @param {Object} coordinates - The coordinates object with latitude and longitude
 * @returns {Promise<boolean>} - Success status
 */
async function saveBusCurrentCoordinates(busNumber, coordinates) {
  try {
    // Format coordinates as string "lat,lng" to match your DB structure
    const coordsString = `${coordinates.latitude},${coordinates.longitude}`;

    const params = {
      TableName: "Buses-DB",
      Key: {
        busNumber: String(busNumber)
      },
      UpdateExpression: "SET currentCoordinates = :coords",
      ExpressionAttributeValues: {
        ":coords": coordsString
      }
    };

    await dynamoDB.send(new UpdateCommand(params));
    return true;
  } catch (error) {
    console.error('Error saving bus current coordinates:', error);
    return false;
  }
}
/**
 * Update bus delay status in DynamoDB
 * @param {string} busNumber - The bus number
 * @param {boolean} isDelayed - Whether the bus is delayed
 * @returns {Promise<boolean>} - Success status
 */
async function updateBusDelayStatus(busNumber, isDelayed) {
  try {
    const params = {
      TableName: "Buses-DB",
      Key: {
        busNumber: String(busNumber)
      },
      UpdateExpression: "SET isDelayed = :isDelayed",
      ExpressionAttributeValues: {
        ":isDelayed": isDelayed
      }
    };

    await dynamoDB.send(new UpdateCommand(params));
    console.log(`Updated delay status for bus ${busNumber} to ${isDelayed}`);
    return true;
  } catch (error) {
    console.error(`Error updating delay status for bus ${busNumber}:`, error);
    return false;
  }
}
/**
 * Get bus data with bus stop coordinates from DynamoDB
 * @param {string} busNumber - The bus number
 * @returns {Promise<Object>} - Object containing bus data and formatted bus stops
 */
async function getBusDataWithStops(busNumber) {
  try {
    // Step 1: Get bus data from Buses-DB
    const busParams = {
      TableName: "Buses-DB",
      Key: {
        busNumber: String(busNumber)
      }
    };

    const busData = await dynamoDB.send(new GetCommand(busParams));
    // console.log("Dy Busdata :", busData);
    if (!busData.Item) {
      console.error(`No data found for bus ${busNumber}`);
      return { busData: null, busStops: [] };
    }

    // Step 2: Extract bus stop names from bus data
    const busStopNames = [];
    const busStopTimings = [];
    // Check if the bus has busStops array directly
    if (busData.Item.busStops && Array.isArray(busData.Item.busStops)) {
      console.log(`Found ${busData.Item.busStops.length} bus stops in bus data`);

      // Extract stop names
      busData.Item.busStops.forEach((stop, index) => {
        if (typeof stop === 'string') {
          // If stop is just a string, use it as the name
          busStopNames.push(stop);
        } else if (stop && stop.name) {
          // If stop is an object with a name property
          busStopNames.push(stop.name);
        }
      });
      // console.log("Bus Stop Names:", busStopNames);

      // Extract timings if available - keep them in an array to maintain position
      if (busData.Item.busStopTiming && Array.isArray(busData.Item.busStopTiming)) {
        console.log(`Found busStopTiming with ${busData.Item.busStopTiming.length} entries`);
        // Simply copy the timing array to maintain the same order as stop names
        busStopTimings.push(...busData.Item.busStopTiming);
      }
    }
    // Fallback to route.stops if busStops array is not present
    else if (busData.Item.route && busData.Item.route.stops) {
      console.log("Using route.stops for bus stop data");

      // Extract stop names and timings from route.stops
      busData.Item.route.stops.forEach(stop => {
        if (stop.name) {
          busStopNames.push(stop.name);
          busStopTimings.push(stop.timing || null);
        }
      });
    }

    // If no bus stops found, return just the bus data
    if (busStopNames.length === 0) {
      console.log(`No bus stops found for bus ${busNumber}`);
      return { busData: busData.Item, busStops: [] };
    }

    console.log(`Processing ${busStopNames.length} bus stops for coordinates`);

    // Step 3: Use the getBusStopCoordinates function to get coordinates
    const busStopsWithCoordinates = await getBusStopCoordinates(busStopNames);
    console.log(`Retrieved ${busStopsWithCoordinates.length} bus stops with coordinates`);

    // Create a map for quick lookup of coordinates by stop name
    const coordinatesMap = {};
    busStopsWithCoordinates.forEach(stop => {
      if (stop && stop.stopName) {
        coordinatesMap[stop.stopName.trim().toLowerCase()] = stop.coordinates || null;
      }
    });

    // Log the number of entries in the coordinates map
    console.log(`Coordinates Map has ${Object.keys(coordinatesMap).length} entries`);
    // Step 4: Format the bus stops with name, timing, and coordinates
    // Use array indices to maintain the correct order and pairinconst normalizedStopName = stopName.trim().toLowerCase();
    
    const formattedBusStops = busStopNames.map((stopName, index) => {
      const normalizedStopName = stopName.trim().toLowerCase();
      const coordinates = coordinatesMap[normalizedStopName] || null;
      
      if (!coordinates) {
        console.log(`No coordinates found for stop: "${stopName}" (normalized: "${normalizedStopName}")`);
      }
      return {
        stopName: stopName,
        coordinates: coordinates,
        timing: busStopTimings[index] || null
      };
    });
    const stopsWithCoordinates = formattedBusStops.filter(stop => stop.coordinates !== null).length;
    console.log(`Successfully processed ${formattedBusStops.length} bus stops, ${stopsWithCoordinates} with coordinates`);
    // Return both the bus data and the formatted bus stops
    return {
      busData: busData.Item,
      busStops: formattedBusStops
    };
  } catch (error) {
    console.error(`Error fetching bus data with stops for bus ${busNumber}:`, error);
    return { busData: null, busStops: [] };
  }
}
/**
 * Fetch bus stop coordinates from BusStops-DB using a scan with filter
 * @param {Array<string>} stopNames - Array of stop names to fetch
 * @returns {Promise<Array>} - Array of bus stop objects with coordinates
 */
async function getBusStopCoordinates(stopNames) {
  if (!stopNames || stopNames.length === 0) {
    return [];
  }

  try {
    // Create an array to store all bus stop data
    const busStops = [];

    // Process each stop name individually
    for (const stopName of stopNames) {
      try {
        // Use ScanCommand with a filter expression using the correct column name 'stopName'
        const command = new ScanCommand({
          TableName: 'BusStops-DB',
          FilterExpression: 'contains(#stopNameAttr, :stopNameValue)',
          ExpressionAttributeNames: {
            '#stopNameAttr': 'stopName'
          },
          ExpressionAttributeValues: {
            ':stopNameValue': stopName
          }
        });

        const response = await dynamoDB.send(command);

        if (response.Items && response.Items.length > 0) {
          // Find the best match if multiple items are returned
          const exactMatch = response.Items.find(item =>
            item.stopName.toLowerCase() === stopName.toLowerCase()
          );

          // Use exact match if found, otherwise use the first item
          const bestMatch = exactMatch || response.Items[0];
          busStops.push(bestMatch);
        } else {
          console.log(`No bus stop found for "${stopName}"`);
        }
      } catch (error) {
        console.error(`Error fetching bus stop "${stopName}":`, error);
        // Continue with other stops even if one fails
      }
    }
    console.log("getBusStopCoordinates completed successfully:", busStops);

    return busStops;
  } catch (error) {
    console.error('Error in getBusStopCoordinates:', error);
    return [];
  }
}

/**
 * Get multiple buses from Buses-DB table using BatchGet for optimization
 * @param {Array<string>} busNumbers - Array of bus numbers to fetch
 * @returns {Promise<Array>} - Array of formatted bus objects
 */
async function getBuses(busNumbers) {
  // If busNumbers is not provided or empty, return empty array
  if (!busNumbers || busNumbers.length === 0) {
    console.log("No bus numbers provided, returning empty array");
    return [];
  }

  // Create an array to store all bus data
  const allBuses = [];

  try {
    // Prepare the keys for BatchGet operation
    const keys = busNumbers.map(busNumber => ({
      busNumber: String(busNumber)
    }));

    // Use BatchGetCommand to fetch multiple buses in a single request
    const command = new BatchGetCommand({
      RequestItems: {
        'Buses-DB': {
          Keys: keys
        }
      }
    });

    const response = await dynamoDB.send(command);

    // Extract the buses from the response
    if (response.Responses && response.Responses['Buses-DB']) {
      allBuses.push(...response.Responses['Buses-DB']);
    }

    // Handle any unprocessed keys if needed
    if (response.UnprocessedKeys &&
      Object.keys(response.UnprocessedKeys).length > 0 &&
      response.UnprocessedKeys['Buses-DB'] &&
      response.UnprocessedKeys['Buses-DB'].Keys) {

      console.log("Some keys were unprocessed, fetching individually:", response.UnprocessedKeys['Buses-DB'].Keys);

      // Fetch any unprocessed keys individually
      for (const key of response.UnprocessedKeys['Buses-DB'].Keys) {
        try {
          const getCommand = new GetCommand({
            TableName: 'Buses-DB',
            Key: key
          });

          const getResponse = await dynamoDB.send(getCommand);
          if (getResponse.Item) {
            allBuses.push(getResponse.Item);
          }
        } catch (error) {
          console.error(`Error fetching unprocessed bus ${JSON.stringify(key)}:`, error);
        }
      }
    }
  } catch (error) {
    console.error('Error in BatchGet operation for buses:', error);

    // Fallback to individual GetCommand requests if BatchGet fails
    console.log('Falling back to individual GetCommand requests');
    for (const busNumber of busNumbers) {
      try {
        const command = new GetCommand({
          TableName: 'Buses-DB',
          Key: {
            busNumber: String(busNumber)
          }
        });

        const response = await dynamoDB.send(command);
        const bus = response.Item;

        if (bus) {
          allBuses.push(bus);
        }
      } catch (error) {
        console.error(`Error fetching bus ${busNumber}:`, error);
        // Continue with other buses even if one fails
      }
    }
  }

  // Get all bus stop coordinates for the stops in these buses
  const allStopNames = new Set();
  allBuses.forEach(bus => {
    if (Array.isArray(bus.busStops)) {
      bus.busStops.forEach(stop => {
        if (typeof stop === 'string') {
          allStopNames.add(stop);
        } else if (stop && stop.name) {
          allStopNames.add(stop.name);
        }
      });
    }
  });

  // Fetch all bus stop coordinates in one batch
  const busStopsData = await getBusStopCoordinates([...allStopNames]);

  // Create a map for quick lookup
  const busStopCoordinatesMap = {};
  busStopsData.forEach(stop => {
    if (stop && stop.stopName) {
      busStopCoordinatesMap[stop.stopName] = stop.coordinates || null;
    }
  });

  // Transform the data to match the expected format for the bus cards
  const formattedBuses = allBuses.map((bus, index) => {
    // Extract stops as an array of strings
    const stops = Array.isArray(bus.busStops) ? bus.busStops : [];

    // Create a route name from first and last stop
    const route = stops.length >= 2
      ? `${stops[0]} to ${stops[stops.length - 1]}`
      : 'No route defined';

    // Format the data to match the Bus interface
    return {
      id: index + 1, // Generate an ID
      number: bus.busNumber || '',
      route: route,
      status: bus.status || 'Active',
      driver: '', // This might need to be fetched from another table
      lastMaintenance: bus.lastMaintenance || new Date().toISOString().split('T')[0],
      currentCoordinates: bus.currentCoordinates || '', // Include current coordinates for live tracking
      stops: stops.map((stop, i) => {
        // Get the stop name (handle both string and object formats)
        const stopName = typeof stop === 'string' ? stop : (stop.name || '');

        // Get the corresponding time if available
        const timeArray = Array.isArray(bus.busStopTiming)
          ? bus.busStopTiming
          : (bus.busStopTiming ? Array.from(bus.busStopTiming) : []);
        const time = timeArray[i] || '';

        // Get coordinates for this stop from our map
        const coordinates = busStopCoordinatesMap[stopName] || null;

        return {
          name: stopName,
          time: time,
          coordinates: coordinates
        };
      })
    };
  });

  return formattedBuses;
}
// Update the export to include the new functions
export {
  getBusMasterData,
  updateBusLocation,
  getBusStops,
  updateBusHistory,
  batchUpdateBusHistory,
  updateBusStatus,
  clearBusCoordinates,
  fetchBusStopDetails,
  checkAndUpdateBusStopHistory,
  updateDriverAssignedBus,
  updateDriverStatus,
  clearBusHistory,
  getBusHistory,
  getAvailableBuses,
  startNewJourney,
  endJourney,
  getBusCurrentCoordinates,
  saveBusCurrentCoordinates,
  updateBusDelayStatus,
  getBusDataWithStops,
  getBusStopCoordinates,
  getBuses
};
