import React, { useState, useEffect } from 'react';
import { View, Text, Button, StyleSheet, Alert, AppState } from 'react-native';
import { startTracking, stopTracking } from '../scripts/LocationTracker';
import * as Location from 'expo-location';

const LocationTrackerExample = () => {
  const [isTracking, setIsTracking] = useState(false);
  const [busNumber, setBusNumber] = useState('123'); // Example bus number
  const [locationPermission, setLocationPermission] = useState(null);
  const [backgroundPermission, setBackgroundPermission] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [appState, setAppState] = useState(AppState.currentState);
  const [lastUpdateTime, setLastUpdateTime] = useState(null);

  // Check permissions and set up app state monitoring on component mount
  useEffect(() => {
    checkPermissions();
    
    // Set up AppState listener
    const appStateSubscription = AppState.addEventListener('change', nextAppState => {
      console.log('App state changed to:', nextAppState);
      setAppState(nextAppState);
      
      if (nextAppState === 'active' && isTracking) {
        // When coming back to foreground, update the last update time
        setLastUpdateTime(new Date().toLocaleTimeString());
      }
    });
    
    // Cleanup on unmount
    return () => {
      appStateSubscription.remove();
    };
  }, [isTracking]);

  // Function to check location permissions
  const checkPermissions = async () => {
    try {
      const foregroundPermission = await Location.getForegroundPermissionsAsync();
      setLocationPermission(foregroundPermission.status);
      
      const backgroundPermission = await Location.getBackgroundPermissionsAsync();
      setBackgroundPermission(backgroundPermission.status);
    } catch (error) {
      console.error('Error checking permissions:', error);
      Alert.alert('Permission Error', 'Failed to check location permissions: ' + error.message);
    }
  };

  // Function to request permissions
  const requestPermissions = async () => {
    try {
      setIsLoading(true);
      const { status: foregroundStatus } = await Location.requestForegroundPermissionsAsync();
      setLocationPermission(foregroundStatus);
      
      if (foregroundStatus === 'granted') {
        const { status: backgroundStatus } = await Location.requestBackgroundPermissionsAsync();
        setBackgroundPermission(backgroundStatus);
        
        if (backgroundStatus !== 'granted') {
          Alert.alert(
            'Background Location Required',
            'This app needs background location permission to track the bus route when the app is in the background or the phone is locked. Note that in Expo Go, background tracking capabilities are limited.',
            [{ text: 'OK' }]
          );
        } else {
          Alert.alert(
            'Background Permission Granted',
            'Background tracking should work, but in Expo Go it may be limited. For full background tracking capabilities, you should build a standalone app.',
            [{ text: 'OK' }]
          );
        }
      }
    } catch (error) {
      console.error('Error requesting permissions:', error);
      Alert.alert('Permission Error', 'Failed to request location permissions: ' + error.message);
    } finally {
      setIsLoading(false);
    }
  };

  // Start tracking function
  const handleStartTracking = async () => {
    if (locationPermission !== 'granted') {
      Alert.alert(
        'Location Permission Required',
        'This app needs location permission to track the bus route.',
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Grant Permission', onPress: requestPermissions }
        ]
      );
      return;
    }
    
    try {
      setIsLoading(true);
      await startTracking(busNumber);
      setIsTracking(true);
      setLastUpdateTime(new Date().toLocaleTimeString());
      
      Alert.alert(
        'Tracking Started',
        `Bus ${busNumber} is now being tracked. Note that in Expo Go, background tracking may only work briefly. For full background tracking capabilities, you should build a standalone app.`,
        [{ text: 'OK' }]
      );
    } catch (error) {
      console.error('Error starting tracking:', error);
      Alert.alert('Error', 'Failed to start tracking: ' + error.message);
    } finally {
      setIsLoading(false);
    }
  };

  // Stop tracking function
  const handleStopTracking = () => {
    try {
      setIsLoading(true);
      stopTracking();
      setIsTracking(false);
      setLastUpdateTime(null);
      Alert.alert('Tracking Stopped', 'Bus location tracking has been stopped.');
    } catch (error) {
      console.error('Error stopping tracking:', error);
      Alert.alert('Error', 'Failed to stop tracking: ' + error.message);
    } finally {
      setIsLoading(false);
    }
  };

  // Helper function to get a user-friendly app state description
  const getAppStateDescription = (state) => {
    switch (state) {
      case 'active': return 'Active (Foreground)';
      case 'background': return 'Background';
      case 'inactive': return 'Inactive (Transitioning)';
      default: return state;
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Bus Location Tracker</Text>
      
      <View style={styles.infoContainer}>
        <Text style={styles.infoTitle}>Expo Go Testing Mode</Text>
        <Text style={styles.infoText}>
          You're running in Expo Go, which has limited background capabilities.
          Full background tracking requires a standalone app build.
        </Text>
      </View>
      
      <View style={styles.permissionContainer}>
        <Text style={styles.permissionText}>
          Foreground Location: <Text style={styles.highlight}>{locationPermission || 'unknown'}</Text>
        </Text>
        <Text style={styles.permissionText}>
          Background Location: <Text style={styles.highlight}>{backgroundPermission || 'unknown'}</Text>
        </Text>
        {(locationPermission !== 'granted' || backgroundPermission !== 'granted') && (
          <Button 
            title="Request Permissions" 
            onPress={requestPermissions} 
            color="#4CAF50"
            disabled={isLoading}
          />
        )}
      </View>
      
      <View style={styles.statusContainer}>
        <Text style={styles.statusText}>
          Tracking Status: <Text style={isTracking ? styles.activeStatus : styles.inactiveStatus}>
            {isTracking ? 'ACTIVE' : 'INACTIVE'}
          </Text>
        </Text>
        <Text style={styles.statusText}>
          App State: <Text style={styles.highlight}>
            {getAppStateDescription(appState)}
          </Text>
        </Text>
        <Text style={styles.busText}>
          Bus Number: <Text style={styles.highlight}>{busNumber}</Text>
        </Text>
        {lastUpdateTime && (
          <Text style={styles.statusText}>
            Last Update: <Text style={styles.highlight}>{lastUpdateTime}</Text>
          </Text>
        )}
      </View>
      
      <View style={styles.buttonContainer}>
        {!isTracking ? (
          <Button
            title={isLoading ? "Starting..." : "Start Tracking"}
            onPress={handleStartTracking}
            color="#2196F3"
            disabled={isTracking || isLoading}
          />
        ) : (
          <Button
            title={isLoading ? "Stopping..." : "Stop Tracking"}
            onPress={handleStopTracking}
            color="#F44336"
            disabled={!isTracking || isLoading}
          />
        )}
      </View>
      
      <Text style={styles.footerText}>
        {isTracking 
          ? "Tracking is active. You can minimize the app to test background behavior, but in Expo Go it may be limited." 
          : "Press Start Tracking to begin monitoring bus location."}
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  infoContainer: {
    backgroundColor: '#fff3cd',
    padding: 15,
    borderRadius: 10,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: '#ffeeba',
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 5,
    color: '#856404',
  },
  infoText: {
    fontSize: 14,
    color: '#856404',
  },
  permissionContainer: {
    backgroundColor: '#fff',
    padding: 15,
    borderRadius: 10,
    marginBottom: 20,
    elevation: 2,
  },
  permissionText: {
    fontSize: 14,
    marginBottom: 10,
  },
  statusContainer: {
    backgroundColor: '#fff',
    padding: 15,
    borderRadius: 10,
    marginBottom: 20,
    elevation: 2,
  },
  statusText: {
    fontSize: 16,
    marginBottom: 10,
  },
  busText: {
    fontSize: 16,
    marginBottom: 10,
  },
  activeStatus: {
    color: '#4CAF50',
    fontWeight: 'bold',
  },
  inactiveStatus: {
    color: '#F44336',
    fontWeight: 'bold',
  },
  highlight: {
    fontWeight: 'bold',
  },
  buttonContainer: {
    marginVertical: 20,
  },
  footerText: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    marginTop: 10,
    fontStyle: 'italic',
  },
});

export default LocationTrackerExample;