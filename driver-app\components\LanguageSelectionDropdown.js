import React, { useState, useContext } from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { ThemeContext } from '../contexts/ThemeContext';
import { LanguageContext } from '../contexts/LanguageContext';

const LanguageSelectionDropdown = () => {
  const { theme } = useContext(ThemeContext);
  const { currentLanguage, changeLanguage } = useContext(LanguageContext);
  const [isOpen, setIsOpen] = useState(false);

  const languages = [
    { code: 'en', name: 'English' },
    // { code: 'hi', name: 'हिंदी' },
    // { code: 'ur', name: 'اردو' },
    { code: 'ta', name: 'தமிழ்' } 
  ];

  const toggleDropdown = () => {
    setIsOpen(!isOpen);
  };

  const selectLanguage = (langCode) => {
    changeLanguage(langCode);
    setIsOpen(false);
  };

  // Find current language display name
  const currentLangName = languages.find(lang => lang.code === currentLanguage)?.name || 'English';

  return (
    <View style={styles.container}>
      <TouchableOpacity
        style={[
          styles.button,
          { backgroundColor: theme.secondaryBackground || theme.normal_background }
        ]}
        onPress={toggleDropdown}
      >
        <Text style={[styles.buttonText, { color: theme.text }]}>
          {currentLangName}
        </Text>
        <MaterialIcons
          name={isOpen ? 'arrow-drop-up' : 'arrow-drop-down'}
          size={24}
          color={theme.text}
        />
      </TouchableOpacity>

      {isOpen && (
        <View
          style={[
            styles.dropdown,
            { backgroundColor: theme.secondaryBackground || theme.normal_background }
          ]}
        >
          {languages.map((lang) => (
            <TouchableOpacity
              key={lang.code}
              style={[
                styles.option,
                currentLanguage === lang.code && {
                  backgroundColor: theme.primary + '20' // 20% opacity
                }
              ]}
              onPress={() => selectLanguage(lang.code)}
            >
              <Text style={[styles.optionText, { color: theme.text }]}>
                {lang.name}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'relative',
    zIndex: 1000,
  },
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1.5,
    elevation: 2,
  },
  buttonText: {
    fontSize: 14,
    fontWeight: '500',
    marginRight: 4,
  },
  dropdown: {
    position: 'absolute',
    top: '100%',
    right: 0,
    marginTop: 5,
    borderRadius: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
    width: 120,
  },
  option: {
    paddingVertical: 10,
    paddingHorizontal: 15,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  optionText: {
    fontSize: 14,
  },
});

export default LanguageSelectionDropdown;