// ReleaseNotes.js - Contains release notes for each app update
// Update this file with each new EAS update

// Current release notes - this will be shown to users after update
export const CURRENT_RELEASE_NOTES = {
  version: "1.0.1",
  date: "2023-04-15",
  notes: [
    "Added internet connectivity monitoring",
    "Implemented No Internet Connection modal with retry and restart options",
    "Improved app stability during network changes",
    "Added multi-language support with translations for English, Hindi, Urdu, and Tamil",
    "Updated all UI components to display text in the selected language",
    "Added language selection option",
    "Implemented confirmation modal requiring bus number verification to end journey or start new trip",
    "Improved UI for journey management with better visual feedback",
    "Fixed background location tracking",
    "Improved battery usage",
    "Enhanced UI responsiveness",
    "add translation support and dynamic font size adjustment"
  ]
};

// Optional: History of previous releases for reference
export const RELEASE_HISTORY = [
  {
    version: "1.0.0",
    date: "2023-04-01",
    notes: [
      "Added multi-language support with translations for English, Hindi, Urdu, and Tamil",
      "Updated all UI components to display text in the selected language",
      "Added language selection option",
      "Implemented confirmation modal requiring bus number verification to end journey or start new trip",
      "Improved UI for journey management with better visual feedback",
      "Fixed background location tracking",
      "Improved battery usage",
      "Enhanced UI responsiveness"
    ]
  },
  {
    version: "0.9.5",
    date: "2023-10-30",
    notes: [
      "Initial beta release",
      "Basic tracking functionality",
      "Driver status updates"
    ]
  }
];