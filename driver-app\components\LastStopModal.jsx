import React, { useContext } from 'react';
import { View, Text, Modal, TouchableOpacity, StyleSheet } from 'react-native';
import { ThemeContext } from '../contexts/ThemeContext';
import { LinearGradient } from 'expo-linear-gradient';
import { useTranslation } from 'react-i18next';

const LastStopModal = ({ visible, onEndJourney, onStartNewJourney, busNumber }) => {
  const { theme, mode } = useContext(ThemeContext);
  const { t } = useTranslation();

  return (
    <Modal
      animationType="slide"
      transparent={true}
      visible={visible}
      onRequestClose={() => {}}
    >
      <View style={styles.modalContainer}>
        <View style={[
          styles.modalContent,
          { 
            backgroundColor: theme.secondaryBackground,
            borderColor: mode === 'dark' ? theme.text : 'transparent',
            borderWidth: mode === 'dark' ? 1 : 0
          }
        ]}>
          <Text style={[styles.modalTitle, { color: theme.text }]}>{t('tracking.lastStopModal.title')}</Text>
          <Text style={[styles.modalText, { color: theme.text }]}>
           {t('tracking.lastStopModal.message', { busNumber: busNumber })}
          </Text>
          
          <View style={styles.modalButtons}>
            <TouchableOpacity 
              style={[styles.modalButton]} 
              onPress={onEndJourney}
            >
              <LinearGradient
                colors={['#f44336', '#d32f2f']}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
                style={styles.buttonGradient}
              >
                <Text style={styles.buttonText}>{t('tracking.lastStopModal.endJourneyButton')}</Text>
              </LinearGradient>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={[styles.modalButton]} 
              onPress={onStartNewJourney}
            >
              <LinearGradient
                colors={mode === 'dark' ? theme.startButtonColor : theme.buttonGradient}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
                style={styles.buttonGradient}
              >
                <Text style={[
                  styles.buttonText, 
                  { color: mode === 'dark' ? theme.startButtonTextColor : theme.normal_text }
                ]}>
                 {t('tracking.lastStopModal.startNewJourneyButton')}
                </Text>
              </LinearGradient>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    paddingHorizontal: 20,
  },
  modalContent: {
    borderRadius: 15,
    padding: 20,
    width: '100%',
    maxWidth: 340,
    alignItems: 'center',
    elevation: 5,
  },
  modalTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    marginBottom: 15,
    textAlign: 'center',
  },
  modalText: {
    fontSize: 16,
    marginBottom: 20,
    textAlign: 'center',
    lineHeight: 22,
  },
  modalButtons: {
    width: '100%',
  },
  modalButton: {
    marginVertical: 8,
    borderRadius: 10,
    overflow: 'hidden',
  },
  buttonGradient: {
    width: '100%',
    padding: 12,
    alignItems: 'center',
    borderRadius: 10,
  },
  buttonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 16,
  },
});

export default LastStopModal;