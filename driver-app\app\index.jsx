import React, { useState, useEffect, useRef } from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { ThemeProvider } from '../contexts/ThemeContext';
import { LanguageProvider } from '../contexts/LanguageContext';
import UIDScreen from './uid';
import NumberScreen from './number';
import Tracking from './tracking';
import ThemedStatusBar from '../components/ThemeStatusBar';
import AsyncStorage from '@react-native-async-storage/async-storage';
import UpdateModal from '../components/UpdateModal';
import NoInternetModal from '../components/NoInternetModal';
import ConnectionRestoredModal from '../components/ConnectionRestoredModal';
import { View, Alert } from 'react-native';
import { getBusMasterData } from '../scripts/DynamoData';
import AnimatedSplashScreen from '../components/AnimatedSplashScreen';
import * as SplashScreen from 'expo-splash-screen';
import NetInfo from '@react-native-community/netinfo';

// Prevent the splash screen from auto-hiding
SplashScreen.preventAutoHideAsync().catch(() => {
  /* reloading the app might trigger some race conditions, ignore them */
});

const Stack = createNativeStackNavigator();

const App = () => {
  const [initialRoute, setInitialRoute] = useState(null);
  const [loading, setLoading] = useState(true);
  const [savedParams, setSavedParams] = useState(null);
  const [showSplash, setShowSplash] = useState(true);
  const [appIsReady, setAppIsReady] = useState(false);
  const [isConnected, setIsConnected] = useState(true);
  const [showNoConnectionModal, setShowNoConnectionModal] = useState(false);
  const [wasDisconnected, setWasDisconnected] = useState(false);
  const [showConnectionRestoredModal, setShowConnectionRestoredModal] = useState(false);
  const navigationRef = useRef(null);

  // Network connectivity check
  useEffect(() => {
    // This will run only once when the component mounts
    const unsubscribe = NetInfo.addEventListener(state => {
      const currentlyConnected = !!state.isConnected;
      
      // If we were disconnected and now we're connected, handle reconnection
      if (!isConnected && currentlyConnected && wasDisconnected) {
        handleReconnection();
      }
      
      // Update connection state
      setIsConnected(currentlyConnected);
      
      // If disconnected, mark that we experienced a disconnection
      if (!currentlyConnected) {
        setWasDisconnected(true);
      }
      
      // Only show modal if we're past splash screen
      if (!showSplash) {
        setShowNoConnectionModal(!currentlyConnected);
      }
    });

    // Initial check
    NetInfo.fetch().then(state => {
      setIsConnected(!!state.isConnected);
    });

    return () => unsubscribe();
  }, [isConnected, showSplash, wasDisconnected]);

  // Handle reconnection logic
  const handleReconnection = async () => {
    try {
      console.log("Internet connection restored, refreshing data...");
      
      // Show the custom connection restored modal
      setShowConnectionRestoredModal(true);
      
      // Get current UID
      const uid = await AsyncStorage.getItem('uid');
      
      if (uid) {
        // Refresh master data
        const freshMasterData = await getBusMasterData("BusMaster-DB", uid);
        
        if (freshMasterData) {
          console.log("Successfully refreshed master data after reconnection");
          
          // Update params with fresh data
          setSavedParams({ masterData: freshMasterData });
          
          // If we're on the tracking screen, navigate back to number screen to reset state
          if (initialRoute === 'Tracking') {
            // Store the current route temporarily
            // const currentRoute = initialRoute;
            
            // Set to Number to trigger navigation
            setInitialRoute('Number');
            
            // Update the savedParams to include the fresh data
            setSavedParams({ 
              masterData: freshMasterData,
              fromReconnection: true 
            });
          }
        }
      }
      
      // Reset the disconnection flag
      setWasDisconnected(false);
      
    } catch (error) {
      console.error("Error refreshing data after reconnection:", error);
    }
  };

  useEffect(() => {
    const checkLogin = async () => {
      try {
        // This will allow our custom AnimatedSplashScreen to show
        try {
          await SplashScreen.hideAsync();
        } catch (e) {
          console.warn("Error hiding splash screen:", e);
        }
        const isLoggedIn = await AsyncStorage.getItem('isLoggedIn');
        const uid = await AsyncStorage.getItem('uid');

        if (isLoggedIn === 'true') {
          // Fetch fresh master data if user is logged in
          if (uid) {
            try {
              console.log("Fetching fresh master data with UID:", uid);
              const freshMasterData = await getBusMasterData("BusMaster-DB", uid);

              if (freshMasterData) {
                console.log("Successfully loaded fresh master data");

                // Always navigate to Number page when logged in
                setInitialRoute('Number');
                setSavedParams({ masterData: freshMasterData });
              } else {
                console.log("Failed to load fresh master data, redirecting to UID");
                setInitialRoute('UID');
              }
            } catch (error) {
              console.error("Error fetching master data:", error);
              setInitialRoute('UID');
            }
          } else {
            // No UID found, redirect to UID screen
            console.log("No UID found in storage, redirecting to UID");
            setInitialRoute('UID');
          }
        } else {
          console.log("User not logged in, navigating to UID screen");
          setInitialRoute('UID');
        }
      } catch (error) {
        console.error("Error checking login status:", error);
        setInitialRoute('UID');
      } finally {
        setLoading(false);
      }
    };

    checkLogin();
  }, []);

  // Handle splash screen completion
  const handleSplashComplete = () => {
    setShowSplash(false);
    // Check connection after splash screen completes
    NetInfo.fetch().then(state => {
      setShowNoConnectionModal(!state.isConnected);
    });
  };

  if (showSplash) {
    return <AnimatedSplashScreen onAnimationComplete={handleSplashComplete} />;
  }

  if (loading || !initialRoute) {
    return <View style={{ flex: 1, backgroundColor: '#fff' }} />;
  }

  return (
    <ThemeProvider>
      <LanguageProvider>
        <ThemedStatusBar />
        <Stack.Navigator
          initialRouteName={initialRoute}
          screenOptions={{
            headerShown: false,
            animation: 'fade',
          }}
        >
          <Stack.Screen name="UID" component={UIDScreen} />
          <Stack.Screen
            name="Number"
            component={NumberScreen}
            initialParams={savedParams}
          />
          <Stack.Screen
            name="Tracking"
            component={Tracking}
            initialParams={initialRoute === 'Tracking' ? savedParams : undefined}
          />
        </Stack.Navigator>
        <UpdateModal />
        <NoInternetModal 
          visible={showNoConnectionModal} 
        />
        <ConnectionRestoredModal
          visible={showConnectionRestoredModal}
          onClose={() => setShowConnectionRestoredModal(false)}
        />
      </LanguageProvider>
    </ThemeProvider>
  );
};

export default App;


